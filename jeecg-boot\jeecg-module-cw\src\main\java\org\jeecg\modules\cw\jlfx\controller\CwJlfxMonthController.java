package org.jeecg.modules.cw.jlfx.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.cw.cbwc.param.CwCbwcSumbitParam;
import org.jeecg.modules.cw.cbwc.result.CwCbwcQueryResult;
import org.jeecg.modules.cw.jlfx.entity.CwJlfxMonth;
import org.jeecg.modules.cw.jlfx.param.CwJlfxSumbitParam;
import org.jeecg.modules.cw.jlfx.result.CwJlfxQueryResult;
import org.jeecg.modules.cw.jlfx.service.ICwJlfxMonthService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 价量分析表
 * @Author: jeecg-boot
 * @Date:   2025-01-03
 * @Version: V1.0
 */
@Api(tags="价量分析表")
@RestController
@RequestMapping("/jlfx/cwJlfxMonth")
@Slf4j
public class CwJlfxMonthController extends JeecgController<CwJlfxMonth, ICwJlfxMonthService> {
	@Autowired
	private ICwJlfxMonthService cwJlfxMonthService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwJlfxMonth
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "价量分析表-分页列表查询")
	@ApiOperation(value="价量分析表-分页列表查询", notes="价量分析表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CwJlfxMonth>> queryPageList(CwJlfxMonth cwJlfxMonth,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwJlfxMonth> queryWrapper = QueryGenerator.initQueryWrapper(cwJlfxMonth, req.getParameterMap());
		Page<CwJlfxMonth> page = new Page<CwJlfxMonth>(pageNo, pageSize);
		IPage<CwJlfxMonth> pageList = cwJlfxMonthService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwJlfxMonth
	 * @return
	 */
	@AutoLog(value = "价量分析表-添加")
	@ApiOperation(value="价量分析表-添加", notes="价量分析表-添加")
	@RequiresPermissions("jlfx:cw_jlfx_month:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwJlfxMonth cwJlfxMonth) {
		cwJlfxMonthService.save(cwJlfxMonth);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwJlfxMonth
	 * @return
	 */
	@AutoLog(value = "价量分析表-编辑")
	@ApiOperation(value="价量分析表-编辑", notes="价量分析表-编辑")
	@RequiresPermissions("jlfx:cw_jlfx_month:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwJlfxMonth cwJlfxMonth) {
		cwJlfxMonthService.updateById(cwJlfxMonth);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "价量分析表-通过id删除")
	@ApiOperation(value="价量分析表-通过id删除", notes="价量分析表-通过id删除")
	@RequiresPermissions("jlfx:cw_jlfx_month:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwJlfxMonthService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "价量分析表-批量删除")
	@ApiOperation(value="价量分析表-批量删除", notes="价量分析表-批量删除")
	@RequiresPermissions("jlfx:cw_jlfx_month:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwJlfxMonthService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "价量分析表-通过id查询")
	@ApiOperation(value="价量分析表-通过id查询", notes="价量分析表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwJlfxMonth> queryById(@RequestParam(name="id",required=true) String id) {
		CwJlfxMonth cwJlfxMonth = cwJlfxMonthService.getById(id);
		if(cwJlfxMonth==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwJlfxMonth);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwJlfxMonth
    */
    @RequiresPermissions("jlfx:cw_jlfx_month:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwJlfxMonth cwJlfxMonth) {
        return super.exportXls(request, cwJlfxMonth, CwJlfxMonth.class, "价量分析表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("jlfx:cw_jlfx_month:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwJlfxMonth.class);
    }

	 @ApiOperation(value = "价量分析表-列表查询", notes = "价量分析表-列表查询")
	 @GetMapping(value = "/query")
	 public Result<CwJlfxQueryResult> query(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
		 CwJlfxQueryResult result = cwJlfxMonthService.queryByDate(queryDate);
		 return Result.ok(result);
	 }

	 @ApiOperation(value = "价量分析表-自动填充", notes = "价量分析表-自动填充")
	 @GetMapping(value = "/autoFill")
	 public Result<CwJlfxQueryResult> autoFill(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
		 CwJlfxQueryResult result = cwJlfxMonthService.autoFill(queryDate);
		 return Result.ok(result);
	 }

	 @ApiOperation(value = "价量分析表-提交", notes = "成本完成表-提交")
	 @PostMapping(value = "/submit")
	 public Result<String> submit(@RequestBody CwJlfxSumbitParam submitParam) {
		 cwJlfxMonthService.submit(submitParam);
		 return Result.OK("提交成功");
	 }
}
