package org.jeecg.modules.cw.base.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 项目名称字典表
 * @Author: jeecg-boot
 * @Date:   2024-12-12
 * @Version: V1.0
 */
public interface CwNameDictMapper extends BaseMapper<CwNameDict> {

    List<CwNameDict> queryListByDw(String dw);
}
