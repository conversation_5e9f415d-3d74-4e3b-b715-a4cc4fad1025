package org.jeecg.modules.cw.jlfx.service;

import org.jeecg.modules.cw.cbwc.param.CwCbwcSumbitParam;
import org.jeecg.modules.cw.cbwc.result.CwCbwcQueryResult;
import org.jeecg.modules.cw.jlfx.entity.CwJlfxMonth;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.cw.jlfx.param.CwJlfxSumbitParam;
import org.jeecg.modules.cw.jlfx.result.CwJlfxQueryResult;

import java.util.Date;

/**
 * @Description: 价量分析表
 * @Author: jeecg-boot
 * @Date:   2025-01-03
 * @Version: V1.0
 */
public interface ICwJlfxMonthService extends IService<CwJlfxMonth> {

    CwJlfxQueryResult queryByDate(Date queryDate);

    void submit(CwJlfxSumbitParam submitParam);

    CwJlfxQueryResult autoFill(Date queryDate);
}
