package org.jeecg.modules.cw.cbgc.service.impl;

import cn.hutool.core.date.DateUtil;
import org.jeecg.modules.cw.cbgc.entity.CwCbgc;
import org.jeecg.modules.cw.cbgc.mapper.CwCbgcMapper;
import org.jeecg.modules.cw.cbgc.service.ICwCbgcService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;
import java.util.List;

/**
 * @Description: 成本构成
 * @Author: jeecg-boot
 * @Date:   2025-02-20
 * @Version: V1.0
 */
@Service
public class CwCbgcServiceImpl extends ServiceImpl<CwCbgcMapper, CwCbgc> implements ICwCbgcService {

    @Override
    public void addOrEdit(List<CwCbgc> cbgcs) {
        // 查看时间和name是否全部相同
        String name = null;
        Date recordTime = null;
        for (CwCbgc cbgc : cbgcs) {
            if (name == null) {
                name = cbgc.getName();
            } else if (!name.equals(cbgc.getName())) {
                return;
            }
            if (recordTime == null) {
                recordTime = cbgc.getRecordTime();
            } else if (!recordTime.equals(cbgc.getRecordTime())) {
                return;
            }
        }
        // 删除再增加
        this.lambdaUpdate().eq(CwCbgc::getName,name)
                .ge(CwCbgc::getRecordTime, DateUtil.beginOfDay(recordTime))
                .le(CwCbgc::getRecordTime, DateUtil.endOfDay(recordTime))
                .remove();
        this.saveBatch(cbgcs);
    }
}
