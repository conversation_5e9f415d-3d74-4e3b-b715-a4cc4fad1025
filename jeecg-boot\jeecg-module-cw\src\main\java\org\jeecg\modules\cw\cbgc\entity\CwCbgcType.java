package org.jeecg.modules.cw.cbgc.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 成本构成（按类型）
 * @Author: jeecg-boot
 * @Date:   2025-02-20
 * @Version: V1.0
 */
@Data
@TableName("cw_cbgc_type")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cw_cbgc_type对象", description="成本构成（按类型）")
public class CwCbgcType implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**材料*/
	@Excel(name = "材料", width = 15)
    @ApiModelProperty(value = "材料")
    private java.math.BigDecimal cl;
	/**备件*/
	@Excel(name = "备件", width = 15)
    @ApiModelProperty(value = "备件")
    private java.math.BigDecimal bj;
	/**燃料*/
	@Excel(name = "燃料", width = 15)
    @ApiModelProperty(value = "燃料")
    private java.math.BigDecimal rl;
	/**工资*/
	@Excel(name = "工资", width = 15)
    @ApiModelProperty(value = "工资")
    private java.math.BigDecimal gz;
	/**制造费用*/
	@Excel(name = "制造费用", width = 15)
    @ApiModelProperty(value = "制造费用")
    private java.math.BigDecimal zzfy;
	/**动力*/
	@Excel(name = "动力", width = 15)
    @ApiModelProperty(value = "动力")
    private java.math.BigDecimal dl;
	/**时间*/
	@Excel(name = "时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "时间")
    private java.util.Date recordTime;
	/**单位*/
	@Excel(name = "单位", width = 15)
    @ApiModelProperty(value = "单位")
    private java.lang.String unit;
}
