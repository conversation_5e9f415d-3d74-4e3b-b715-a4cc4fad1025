package org.jeecg.modules.cw.base.service.impl;

import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.mapper.CwNameDictMapper;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 项目名称字典表
 * @Author: jeecg-boot
 * @Date:   2024-12-12
 * @Version: V1.0
 */
@Service
public class CwNameDictServiceImpl extends ServiceImpl<CwNameDictMapper, CwNameDict> implements ICwNameDictService {

    @Resource
    private CwNameDictMapper cwNameDictMapper;

    @Override
    public List<CwNameDict> queryList(String dw) {
        return cwNameDictMapper.queryListByDw(dw);
    }
}
