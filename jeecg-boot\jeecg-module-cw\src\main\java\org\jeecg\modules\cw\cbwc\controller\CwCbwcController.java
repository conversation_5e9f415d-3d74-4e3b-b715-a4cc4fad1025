package org.jeecg.modules.cw.cbwc.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.cw.cbwc.entity.CwCbwc;
import org.jeecg.modules.cw.cbwc.param.CwCbwcSumbitParam;
import org.jeecg.modules.cw.cbwc.result.CwCbwcQueryResult;
import org.jeecg.modules.cw.cbwc.service.ICwCbwcService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.cw.xjs.param.CwXjsZhcbSumbitParam;
import org.jeecg.modules.cw.xjs.result.CwXjsZhcbListResult;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 成本完成表
 * @Author: jeecg-boot
 * @Date:   2024-12-31
 * @Version: V1.0
 */
@Api(tags="成本完成表")
@RestController
@RequestMapping("/cbwc/cwCbwc")
@Slf4j
public class CwCbwcController extends JeecgController<CwCbwc, ICwCbwcService> {
	@Autowired
	private ICwCbwcService cwCbwcService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwCbwc
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "成本完成表-分页列表查询")
	@ApiOperation(value="成本完成表-分页列表查询", notes="成本完成表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CwCbwc>> queryPageList(CwCbwc cwCbwc,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwCbwc> queryWrapper = QueryGenerator.initQueryWrapper(cwCbwc, req.getParameterMap());
		Page<CwCbwc> page = new Page<CwCbwc>(pageNo, pageSize);
		IPage<CwCbwc> pageList = cwCbwcService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwCbwc
	 * @return
	 */
	@AutoLog(value = "成本完成表-添加")
	@ApiOperation(value="成本完成表-添加", notes="成本完成表-添加")
	@RequiresPermissions("cbwc:cw_cbwc:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwCbwc cwCbwc) {
		cwCbwcService.save(cwCbwc);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwCbwc
	 * @return
	 */
	@AutoLog(value = "成本完成表-编辑")
	@ApiOperation(value="成本完成表-编辑", notes="成本完成表-编辑")
	@RequiresPermissions("cbwc:cw_cbwc:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwCbwc cwCbwc) {
		cwCbwcService.updateById(cwCbwc);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "成本完成表-通过id删除")
	@ApiOperation(value="成本完成表-通过id删除", notes="成本完成表-通过id删除")
	@RequiresPermissions("cbwc:cw_cbwc:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwCbwcService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "成本完成表-批量删除")
	@ApiOperation(value="成本完成表-批量删除", notes="成本完成表-批量删除")
	@RequiresPermissions("cbwc:cw_cbwc:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwCbwcService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "成本完成表-通过id查询")
	@ApiOperation(value="成本完成表-通过id查询", notes="成本完成表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwCbwc> queryById(@RequestParam(name="id",required=true) String id) {
		CwCbwc cwCbwc = cwCbwcService.getById(id);
		if(cwCbwc==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwCbwc);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwCbwc
    */
    @RequiresPermissions("cbwc:cw_cbwc:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwCbwc cwCbwc) {
        return super.exportXls(request, cwCbwc, CwCbwc.class, "成本完成表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("cbwc:cw_cbwc:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwCbwc.class);
    }


	 @ApiOperation(value = "成本完成表-列表查询", notes = "成本完成表-列表查询")
	 @GetMapping(value = "/query")
	 public Result<CwCbwcQueryResult> query(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
		 CwCbwcQueryResult result = cwCbwcService.queryByDate(queryDate);
		 return Result.ok(result);
	 }

	 @ApiOperation(value = "成本完成表-自动填充", notes = "成本完成表-自动填充")
	 @GetMapping(value = "/autoFill")
	 public Result<CwCbwcQueryResult> autoFill(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
		 CwCbwcQueryResult result = cwCbwcService.autoFill(queryDate);
		 return Result.ok(result);
	 }

	 @ApiOperation(value = "成本完成表-提交", notes = "成本完成表-提交")
	 @PostMapping(value = "/submit")
	 public Result<String> submit(@RequestBody CwCbwcSumbitParam submitParam) {
		 cwCbwcService.submit(submitParam);
		 return Result.OK("提交成功");
	 }
}
