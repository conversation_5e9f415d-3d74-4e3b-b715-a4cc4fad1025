package org.jeecg.modules.cw.jw.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.cw.jw.param.CwJwZhcbSumbitParam;
import org.jeecg.modules.cw.jw.result.CwJwZhcbListResult;
import org.jeecg.modules.cw.jw.service.ICwJwZhcbService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Description: 精尾厂-综合成本表
 * @Author: tang
 * @Date: 2024-12-06
 * @Version: V1.0
 */
@Api(tags = "精尾厂-综合财报")
@RestController
@RequestMapping("/jw/zhcb")
@Slf4j
public class CwJwZhcbController {

    @Resource
    private ICwJwZhcbService jwZhcbService;


    @ApiOperation(value = "精尾厂-综合成本-列表查询", notes = "精尾厂-综合成本-列表查询")
    @GetMapping(value = "/query")
    public Result<CwJwZhcbListResult> query(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        CwJwZhcbListResult result = jwZhcbService.query(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "精尾厂-综合成本-自动填充", notes = "精尾厂-综合成本-自动填充")
    @GetMapping(value = "/autoFill")
    public Result<CwJwZhcbListResult> autoFill(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        CwJwZhcbListResult result = jwZhcbService.autoFill(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "精尾厂-综合成本-提交", notes = "精尾厂-综合成本-提交")
    @PostMapping(value = "/submit")
    public Result<String> submit(@RequestBody CwJwZhcbSumbitParam submitParam) {
        jwZhcbService.submit(submitParam);
        return Result.OK("提交成功");
    }
}
