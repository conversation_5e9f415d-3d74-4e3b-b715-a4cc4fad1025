package org.jeecg.modules.cw.frdw.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.cw.frdw.entity.CwFrdwDay;
import org.jeecg.modules.cw.frdw.param.CwFrdwSumbitParam;
import org.jeecg.modules.cw.frdw.result.CwFrdwListResult;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 法人单位
 * @Author: jeecg-boot
 * @Date:   2025-01-09
 * @Version: V1.0
 */
public interface ICwFrdwService extends IService<CwFrdwDay> {
    /**
     * 获取当天的法人单位利润总和
     * @param queryDate 查询日期
     * @return 法人单位利润总和
     */
    BigDecimal sumDay(Date queryDate);
    
    /**
     * 获取当月的法人单位利润总和
     * @param queryDate 月份的任一日期
     * @return 法人单位利润总和
     */
    BigDecimal sumMonth(Date queryDate);
    
    /**
     * 查询指定日期范围内的法人单位利润总和
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 法人单位利润总和
     */
    BigDecimal sumPeriod(Date startDate, Date endDate);

    CwFrdwListResult query(Date queryDate);

    void submit(CwFrdwSumbitParam param);

    CwFrdwListResult autoFill(Date queryDate);
}
