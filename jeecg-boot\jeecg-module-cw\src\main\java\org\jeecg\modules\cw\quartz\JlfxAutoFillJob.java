package org.jeecg.modules.cw.quartz;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.log4j.Log4j2;
import org.jeecg.modules.cw.jlfx.entity.CwJlfxMonth;
import org.jeecg.modules.cw.jlfx.service.ICwJlfxMonthService;
import org.jeecg.modules.cw.jscb.entity.CwJscb;
import org.jeecg.modules.cw.jscb.service.ICwJscbService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Log4j2
@Service
public class JlfxAutoFillJob  implements Job {

    @Resource
    private ICwJlfxMonthService jlfxService;

    @Override
    public void execute(JobExecutionContext ctx) throws JobExecutionException {
        Date date = new Date();
        try {
            // 获取上个月的数据
            int offset = -1;
            DateTime offsetMonth = DateUtil.offsetMonth(date, offset);
            while (true) {
                Long count = jlfxService.lambdaQuery()
                        .between(CwJlfxMonth::getRecordTime, DateUtil.beginOfMonth(offsetMonth), DateUtil.endOfMonth(offsetMonth))
                        .count();
                if (count > 0 || offset < -20) {
                    break;
                }
                offset--;
                offsetMonth = DateUtil.offsetMonth(date, offset);
            }
            List<CwJlfxMonth> jscbList = jlfxService.lambdaQuery()
                    .between(CwJlfxMonth::getRecordTime, DateUtil.beginOfMonth(offsetMonth), DateUtil.endOfMonth(offsetMonth))
                    .list();
            // 更新时间
            if (ObjectUtil.isNotEmpty(jscbList)) {
                for (CwJlfxMonth jscb : jscbList) {
                    DateTime recordTime = DateUtil.offsetMonth(jscb.getRecordTime(), -1 * offset);
                    jscb.setRecordTime(recordTime);
                    jscb.setId(null);
                }
                jlfxService.saveBatch(jscbList);
            }
            log.info("价量分析填充成功");
        } catch (Exception e) {
            log.error("自动填充价量分析成本失败", e);
        }
    }
}
