package org.jeecg.modules.cw.krb.entity;

import cn.hutool.core.map.MapUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CwKrbRow {
    private String name;
    private String unit;
    private BigDecimal drs;
    private BigDecimal rlj;
    private BigDecimal yys;
    private String remark;
    private String dw;

    public static final String CL = "cl";
    public static final String BJ = "bj";
    public static final String RL = "rl";
    public static final String S = "s";
    public static final String D = "d";
    public static final String GZ = "gz";
    public static final String GZXFY = "gzxfy";
    public static final String ZZFY = "zzfy";
    public static final String ZJF = "zjf";
    public static Map<String, CwKrbRow> takeDefaultKrbRows(String dw) {
        return MapUtil.builder(new HashMap<String, CwKrbRow>())
                .put(CL, new CwKrbRow("材料", "万元", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "", dw))
                .put(BJ, new CwKrbRow("备件", "万元", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "", dw))
                .put(RL, new CwKrbRow("燃料", "万元", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "", dw))
                .put(S, new CwKrbRow("水", "万元", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "", dw))
                .put(D, new CwKrbRow("电", "万元", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "", dw))
                .put(GZ, new CwKrbRow("工资", "万元", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "", dw))
                .put(GZXFY, new CwKrbRow("工资性费用", "万元", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "", dw))
                .put(ZZFY, new CwKrbRow("制造费用", "万元", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "", dw))
                .put(ZJF, new CwKrbRow("折旧费", "万元", BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "", dw))
                .build();
    }
}
