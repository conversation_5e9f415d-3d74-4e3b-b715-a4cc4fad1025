package org.jeecg.modules.cw.jw.mapper;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.cw.jw.entity.CwJwDay;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 精尾厂-日填报
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
public interface CwJwDayMapper extends BaseMapper<CwJwDay> {

    BigDecimal sum(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
}
