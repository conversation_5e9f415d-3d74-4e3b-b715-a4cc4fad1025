package org.jeecg.modules.cw.quartz;

import cn.hutool.core.date.DateUtil;
import lombok.extern.log4j.Log4j2;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrStatisticsDayService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 当天模拟利润重新计算 Job
 * 执行时会对当天数据进行重算，适用于日终补录后立即刷新统计。
 */
@Log4j2
@Service
public class RecalcTodayMnlrJob implements Job {

    @Resource
    private ICwMnlrStatisticsDayService statisticsDayService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        Date today = DateUtil.beginOfDay(new Date());
        try {
            log.info("[RecalcTodayMnlrJob] 开始重算 {} 的模拟利润", DateUtil.formatDate(today));
            statisticsDayService.recalcDay(today);
            log.info("[RecalcTodayMnlrJob] 重算完成 {}", DateUtil.formatDate(today));
        } catch (Exception e) {
            log.error("[RecalcTodayMnlrJob] 重算当天模拟利润出现异常", e);
            throw new JobExecutionException(e);
        }
    }
} 