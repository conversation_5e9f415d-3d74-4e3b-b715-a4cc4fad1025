package org.jeecg.modules.cw.jscb.service;


import org.jeecg.modules.cw.jscb.entity.CwJsZcb;
import org.jeecg.modules.cw.jscb.entity.CwJscb;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.cw.jscb.param.CwJscbSumbitParam;
import org.jeecg.modules.cw.jscb.result.CwJscbQueryResult;

import java.util.Date;
import java.util.List;

/**
 * @Description: 金属成本
 * @Author: jeecg-boot
 * @Date:   2025-01-15
 * @Version: V1.0
 */
public interface ICwJscbService extends IService<CwJscb> {

    CwJscbQueryResult queryByDate(Date queryDate);

    void submit(CwJscbSumbitParam submitParam);

    List<CwJsZcb> getJscb(Date queryDate);

    CwJscbQueryResult autoFill(Date queryDate);
}
