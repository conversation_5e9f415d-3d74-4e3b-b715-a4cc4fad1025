package org.jeecg.modules.cw.jg.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.ckc.entity.CwCkcDay;
import org.jeecg.modules.cw.jg.entity.CwJgDay;
import org.jeecg.modules.cw.jg.entity.CwJgRow;
import org.jeecg.modules.cw.jg.param.CwJgZhcbSumbitParam;
import org.jeecg.modules.cw.jg.result.CwJgZhcbListResult;
import org.jeecg.modules.cw.jg.service.ICwJgDayService;
import org.jeecg.modules.cw.jg.service.ICwJgZhcbService;
import org.jeecg.modules.cw.krb.entity.CwKrbRow;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 机关-日填报
 * @Author: jeecg-boot
 * @Date: 2024-12-06
 * @Version: V1.0
 */
@Service
public class CwJgZhcbServiceImpl implements ICwJgZhcbService {

    private static final String DICT_TYPE = "jg";
    private static final String CACHE_KEY = "cw:jg:zhcb:";
    private static final String QUERY_CACHE_KEY = CACHE_KEY + "query";
    private static final String SUM_BY_MONTH_CACHE_KEY = CACHE_KEY + "sumByMonth";

    @Resource
    private ICwNameDictService nameDictService;
    @Resource
    private ICwJgDayService jgDayService;


    @Override
//    @Cacheable(cacheNames = QUERY_CACHE_KEY, key = "#queryDate")
    public CwJgZhcbListResult query(Date queryDate) {
        CwJgZhcbListResult result = new CwJgZhcbListResult();
        result.setQueryDate(queryDate);
        // 项目字典
        List<CwNameDict> dict = nameDictService.queryList(DICT_TYPE);
        // 已有列表
        List<CwJgDay> allDays = jgDayService.lambdaQuery()
                .between(CwJgDay::getRecordTime, DateUtil.beginOfMonth(queryDate), DateUtil.endOfDay(queryDate))
                .list();
        List<CwJgDay> days = allDays.stream()
                .filter(d -> DateUtil.isSameDay(queryDate, d.getRecordTime()))
                .collect(Collectors.toList());
        // 合并数据
        List<CwJgRow> resRows = new ArrayList<>();
        for (CwNameDict d : dict) {
            CwJgRow row = new CwJgRow();
            BeanUtil.copyProperties(d, row);
            // 数据
            days.stream().filter(d1 -> d.getName().equals(d1.getName()))
                    .findFirst()
                    .ifPresent(d1 -> {
                        row.setDrs(d1.getDrs());
                        row.setRemark(d1.getRemark());
                    });
            // 月累计 注！不包括当天
            BigDecimal sum = allDays.stream()
                    .filter(v -> d.getName().equals(v.getName())
                            && ObjectUtil.isNotEmpty(v.getDrs())
                            && !DateUtil.isSameDay(v.getRecordTime(), queryDate))
                    .map(CwJgDay::getDrs)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            row.setYlj(sum);
            resRows.add(row);
        }
        result.setRows(resRows);
        // 结果
        return result;
    }

    @Override
    @CacheEvict(cacheNames = {QUERY_CACHE_KEY, SUM_BY_MONTH_CACHE_KEY}, allEntries = true)
    public void submit(CwJgZhcbSumbitParam param) {
        Date submitDate = param.getSubmitDate();
        // 更新行数据
        List<CwJgRow> rows = param.getRows();
        jgDayService.remove(new LambdaQueryWrapper<>(CwJgDay.class).eq(CwJgDay::getRecordTime, submitDate));
        ArrayList<CwJgDay> days = new ArrayList<>();
        for (CwJgRow row : rows) {
            CwJgDay day = new CwJgDay();
            BeanUtil.copyProperties(row, day);
            day.setRecordTime(submitDate);
            days.add(day);
        }
        jgDayService.saveBatch(days);
    }

    @Override
//    @Cacheable(cacheNames = SUM_BY_MONTH_CACHE_KEY, key = "#queryDate")
    public List<CwKrbRow> sumByMonth(Date queryDate) {
        Map<String, CwKrbRow> result = CwKrbRow.takeDefaultKrbRows("矿部机关");
        // 准备数据
        List<CwJgDay> allDays = jgDayService.lambdaQuery()
                .between(CwJgDay::getRecordTime, DateUtil.beginOfMonth(queryDate), DateUtil.endOfDay(queryDate))
                .list();
        // 计算
        CwKrbRow clRow = result.get(CwKrbRow.CL);
        CwKrbRow bjRow = result.get(CwKrbRow.BJ);
        CwKrbRow rlRow = result.get(CwKrbRow.RL);
        CwKrbRow sRow = result.get(CwKrbRow.S);
        CwKrbRow dRow = result.get(CwKrbRow.D);
        CwKrbRow gzRow = result.get(CwKrbRow.GZ);
        CwKrbRow gzxfyRow = result.get(CwKrbRow.GZXFY);
        CwKrbRow zzfyRow = result.get(CwKrbRow.ZZFY);
        CwKrbRow zjfRow = result.get(CwKrbRow.ZJF);
        for (CwJgDay d : allDays) {
            BigDecimal drs = d.getDrs();
            if (ObjectUtil.isNotEmpty(drs)) {
                // 当日数
                if (DateUtil.isSameDay(queryDate, d.getRecordTime())) {
                    switch (d.getType()) {
                        case "cl":
                            clRow.setDrs(clRow.getDrs().add(drs));
                            break;
                        case "bj":
                            bjRow.setDrs(bjRow.getDrs().add(drs));
                            break;
                        case "rl":
                            rlRow.setDrs(rlRow.getDrs().add(drs));
                            break;
                        case "d":
                            dRow.setDrs(dRow.getDrs().add(drs));
                            break;
                        case "s":
                            sRow.setDrs(sRow.getDrs().add(drs));
                            break;
                        case "gz":
                            gzRow.setDrs(gzRow.getDrs().add(drs));
                            break;
                        case "gzxfy":
                            gzxfyRow.setDrs(gzxfyRow.getDrs().add(drs));
                            break;
                        case "zzfy":
                            zzfyRow.setDrs(zzfyRow.getDrs().add(drs));
                            break;
                        case "zjf":
                            zjfRow.setDrs(zjfRow.getDrs().add(drs));
                            break;
                    }
                }
                // 月累计
                switch (d.getType()) {
                    case "cl":
                        clRow.setRlj(clRow.getRlj().add(drs));
                        break;
                    case "bj":
                        bjRow.setRlj(bjRow.getRlj().add(drs));
                        break;
                    case "rl":
                        rlRow.setRlj(rlRow.getRlj().add(drs));
                        break;
                    case "d":
                        dRow.setRlj(dRow.getRlj().add(drs));
                        break;
                    case "s":
                        sRow.setRlj(sRow.getRlj().add(drs));
                        break;
                    case "gz":
                        gzRow.setRlj(gzRow.getRlj().add(drs));
                        break;
                    case "gzxfy":
                        gzxfyRow.setRlj(gzxfyRow.getRlj().add(drs));
                        break;
                    case "zzfy":
                        zzfyRow.setRlj(zzfyRow.getRlj().add(drs));
                        break;
                    case "zjf":
                        zjfRow.setRlj(zjfRow.getRlj().add(drs));
                        break;
                }
            }
        }
        // 结果
        return new ArrayList<>(result.values());
    }

    @Override
    public CwJgZhcbListResult autoFill(Date queryDate) {
        // 1号不填充自己
        if (DateUtil.dayOfMonth(queryDate) == 1) {
            return query(queryDate);
        }

        Date dateToCopyFrom = DateUtil.beginOfMonth(queryDate);
        long count = jgDayService.lambdaQuery()
                .between(CwJgDay::getRecordTime, DateUtil.beginOfDay(dateToCopyFrom), DateUtil.endOfDay(dateToCopyFrom))
                .count();

        if (count > 0) {
            // 找到数据，立即使用这个日期进行查询并返回
            return query(dateToCopyFrom);
        }

        // 如果1号没数据，则默认查询当天
        return query(queryDate);
    }
}
