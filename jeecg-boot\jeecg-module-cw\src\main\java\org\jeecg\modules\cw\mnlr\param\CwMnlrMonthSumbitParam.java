package org.jeecg.modules.cw.mnlr.param;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.modules.cw.cbwc.entity.CwCbwcRow;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrMonthRow;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CwMnlrMonthSumbitParam {
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;
    private List<CwMnlrMonthRow> rows;
    private BigDecimal qtfy;
    private BigDecimal frdw;
    private BigDecimal jh;
    private BigDecimal tzlr;
}
