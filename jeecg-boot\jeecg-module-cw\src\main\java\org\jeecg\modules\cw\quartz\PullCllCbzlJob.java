package org.jeecg.modules.cw.quartz;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.stream.CollectorUtil;
import cn.hutool.core.stream.StreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.log4j.Log4j;
import lombok.extern.log4j.Log4j2;
import org.jeecg.modules.cw.base.constants.CwCllDataName;
import org.jeecg.modules.cw.base.entity.CwCllCblData;
import org.jeecg.modules.cw.base.entity.CwCllData;
import org.jeecg.modules.cw.base.service.ICwCllCblDataService;
import org.jeecg.modules.cw.base.service.ICwCllDataService;
import org.jeecg.modules.cw.quartz.dto.CblDTO;
import org.jeecg.modules.cw.quartz.dto.CllDTO;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@Service
public class PullCllCbzlJob implements Job {

    @Resource
    private ICwCllCblDataService cllCblDataService;
    
    @Resource
    private ICwCllDataService cwCllDataService;

    @Override
    public void execute(JobExecutionContext ctx) throws JobExecutionException {
        // 获取token
        String tokenJson = HttpUtil.get("http://172.18.139.34:8310/oauth/token?client_id=4d0a82b0ba124cf78f1a9ae89f31fc31&client_secret=tsfk3uq!");
        if (ObjectUtil.isEmpty(tokenJson)) {
            log.error("获取token失败");
            return;
        }
        String accessToken = (String) JSONObject.parseObject(tokenJson).get("access_token");
        // 获取处理量
        String cllJson = HttpUtil
                .get("http://172.18.139.34:8310/service/api/mnlrxt_dsszrjykcll?client_id=4d0a82b0ba124cf78f1a9ae89f31fc31&access_token=" + accessToken + "&pageSize=10&pageNumber=1");
        String cllResult = JSONObject.parseObject(cllJson).getString("result");
        List<CllDTO> cllDTO = JSONObject.parseArray(cllResult, CllDTO.class);
        // 采拨量
        String cbzlResult = HttpUtil.get("http://172.18.139.34:8310/service/api/mnlrxt_ckcrjcbzl?client_id=4d0a82b0ba124cf78f1a9ae89f31fc31&access_token=" + accessToken + "&pageSize=10&pageNumber=1");
        String cblResult = JSONObject.parseObject(cbzlResult).getString("result");
        List<CblDTO> cblDTO = JSONObject.parseArray(cblResult, CblDTO.class);
        // 处理数据
        Map<String, CllDTO> date2Cll = CollStreamUtil.toIdentityMap(cllDTO, CllDTO::getTjrq);
        Map<String, CblDTO> date2Cbl = CollStreamUtil.toIdentityMap(cblDTO, CblDTO::getTjrq);

        // 并集
        Set<String> dateList = StreamUtil.of(CollUtil.union(CollUtil.newArrayList(date2Cll.keySet()), CollUtil.newArrayList(date2Cbl.keySet())))
                .collect(Collectors.toSet());

        for (String date : dateList) {
            Date recordTime = Date.from(DateUtil.parse(date).toInstant());
            
            CllDTO cll = date2Cll.get(date);

            // 泗州处理量
            if (ObjectUtil.isNotEmpty(cll) && ObjectUtil.isNotEmpty(cll.getSzrjykcll())) {
                // 接口返回单位：吨，数据库统一存储单位：万吨
                BigDecimal szCll = new BigDecimal(cll.getSzrjykcll())
                        .divide(new BigDecimal(10000), 4, BigDecimal.ROUND_HALF_UP);
                cwCllDataService.setCllData(CwCllDataName.SZCLL, szCll.stripTrailingZeros().toPlainString(), recordTime);
            }

            // 大山处理量
            if (ObjectUtil.isNotEmpty(cll) && ObjectUtil.isNotEmpty(cll.getDsrjykcll())) {
                BigDecimal dsCll = new BigDecimal(cll.getDsrjykcll())
                        .divide(new BigDecimal(10000), 4, BigDecimal.ROUND_HALF_UP);
                cwCllDataService.setCllData(CwCllDataName.DSCLL, dsCll.stripTrailingZeros().toPlainString(), recordTime);
            }

            // 采拨总量
            CblDTO cbl = date2Cbl.get(date);
            if (ObjectUtil.isNotEmpty(cbl) && ObjectUtil.isNotEmpty(cbl.getRjcbzl())) {
                BigDecimal cbzl = new BigDecimal(cbl.getRjcbzl())
                        .divide(new BigDecimal(10000), 4, BigDecimal.ROUND_HALF_UP);
                cwCllDataService.setCllData(CwCllDataName.CBZL, cbzl.stripTrailingZeros().toPlainString(), recordTime);
            }
        }
        
        log.info("拉取处理量、采拨量数据完成，数据已存储到cw_cll_data表");
    }
}
