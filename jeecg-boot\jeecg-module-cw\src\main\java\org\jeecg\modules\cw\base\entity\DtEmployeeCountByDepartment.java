package org.jeecg.modules.cw.base.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * Entity corresponding to table dt_employee_count_by_department (德铜人员统计).
 *
 * <AUTHOR>
 */
@Data
@TableName("dt_employee_count_by_department")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class DtEmployeeCountByDepartment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 部门名称（作为复合主键的一部分）
     */
    @TableId
    private String depDisplayName;

    /**
     * 统计日期（作为复合主键的一部分）
     */
    private Date qDate;

    /**
     * 人数
     */
    private Integer employeeCount;
} 