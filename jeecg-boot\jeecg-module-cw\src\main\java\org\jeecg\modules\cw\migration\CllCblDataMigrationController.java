package org.jeecg.modules.cw.migration;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.cw.base.constants.CwCllDataName;
import org.jeecg.modules.cw.base.entity.CwCllCblData;
import org.jeecg.modules.cw.base.service.ICwCllCblDataService;
import org.jeecg.modules.cw.base.service.ICwCllDataService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 处理量采拨量数据迁移控制器
 * 提供手动触发数据迁移的接口
 */
@Log4j2
@RestController
@RequestMapping("/cw/migration")
@Api(tags = "数据迁移")
public class CllCblDataMigrationController {

    @Resource
    private ICwCllCblDataService cwCllCblDataService;

    @Resource
    private ICwCllDataService cwCllDataService;

    /**
     * 手动触发数据迁移
     * 将 cw_cll_cbl_data 表中的数据迁移到 cw_cll_data 表中
     * @return 迁移结果
     */
    @GetMapping("/cll-cbl-data")
    @ApiOperation(value = "迁移处理量采拨量数据", notes = "将cw_cll_cbl_data表中的数据迁移到cw_cll_data表中")
    public Result<String> migrateCllCblData() {
        try {
            log.info("开始迁移处理量采拨量数据...");
            
            // 查询所有数据
            List<CwCllCblData> cllCblDataList = cwCllCblDataService.list();
            
            if (cllCblDataList == null || cllCblDataList.isEmpty()) {
                log.info("没有需要迁移的数据");
                return Result.OK("没有需要迁移的数据");
            }
            
            log.info("共找到 {} 条需要迁移的数据", cllCblDataList.size());
            
            // 迁移数据
            int successCount = 0;
            for (CwCllCblData cllCblData : cllCblDataList) {
                try {
                    // 迁移泗州处理量
                    if (cllCblData.getSzcll() != null) {
                        cwCllDataService.setCllData(CwCllDataName.SZCLL, cllCblData.getSzcll().toString(), cllCblData.getRecordTime());
                    }
                    
                    // 迁移大山处理量
                    if (cllCblData.getDscll() != null) {
                        cwCllDataService.setCllData(CwCllDataName.DSCLL, cllCblData.getDscll().toString(), cllCblData.getRecordTime());
                    }
                    
                    // 迁移采拨总量
                    if (cllCblData.getCbzl() != null) {
                        cwCllDataService.setCllData(CwCllDataName.CBZL, cllCblData.getCbzl().toString(), cllCblData.getRecordTime());
                    }
                    
                    successCount++;
                } catch (Exception e) {
                    log.error("迁移数据 {} 时发生错误", cllCblData.getId(), e);
                }
            }
            
            String message = String.format("数据迁移完成，共迁移 %d/%d 条数据", successCount, cllCblDataList.size());
            log.info(message);
            return Result.OK(message);
        } catch (Exception e) {
            log.error("数据迁移过程中发生错误", e);
            return Result.error("数据迁移失败：" + e.getMessage());
        }
    }
} 