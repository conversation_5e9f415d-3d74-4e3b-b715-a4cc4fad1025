package org.jeecg.modules.cw.ds.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.cw.ds.param.CwDsZhcbSumbitParam;
import org.jeecg.modules.cw.ds.result.CwDsZhcbListResult;
import org.jeecg.modules.cw.ds.service.ICwDsZhcbService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Description: 大山厂-综合成本表
 * @Author: tang
 * @Date: 2024-12-06
 * @Version: V1.0
 */
@Api(tags = "大山厂-综合财报")
@RestController
@RequestMapping("/ds/zhcb")
@Slf4j
public class CwDsZhcbController {

    @Resource
    private ICwDsZhcbService dsZhcbService;


    @ApiOperation(value = "大山厂-综合成本-列表查询", notes = "大山厂-综合成本-列表查询")
    @GetMapping(value = "/query")
    public Result<CwDsZhcbListResult> query(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        CwDsZhcbListResult result = dsZhcbService.query(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "大山厂-综合成本-自动填充", notes = "大山厂-综合成本-自动填充")
    @GetMapping(value = "/autoFill")
    public Result<CwDsZhcbListResult> autoFill(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        CwDsZhcbListResult result = dsZhcbService.autoFill(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "大山厂-综合成本-提交", notes = "大山厂-综合成本-提交")
    @PostMapping(value = "/submit")
    public Result<String> submit(@RequestBody CwDsZhcbSumbitParam submitParam) {
        dsZhcbService.submit(submitParam);
        return Result.OK("提交成功");
    }
}
