package org.jeecg.modules.cw.mnlr.service;

import org.jeecg.modules.cw.cbwc.param.CwCbwcSumbitParam;
import org.jeecg.modules.cw.cbwc.result.CwCbwcQueryResult;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrMonth;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.cw.mnlr.param.CwMnlrMonthSumbitParam;
import org.jeecg.modules.cw.mnlr.result.CwMnlrMonthQueryResult;

import java.util.Date;

/**
 * @Description: 矿模拟利润（月）
 * @Author: jeecg-boot
 * @Date:   2025-01-07
 * @Version: V1.0
 */
public interface ICwMnlrMonthService extends IService<CwMnlrMonth> {

    CwMnlrMonthQueryResult queryByDate(Date queryDate);

    void submit(CwMnlrMonthSumbitParam submitParam);
}
