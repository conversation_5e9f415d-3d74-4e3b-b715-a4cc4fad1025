package org.jeecg.modules.cw.mnlr.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrDay;
import org.jeecg.modules.cw.mnlr.param.CwMnlrDaySumbitParam;
import org.jeecg.modules.cw.mnlr.param.CwMnlrMonthSumbitParam;
import org.jeecg.modules.cw.mnlr.result.CwMnlrDayQueryResult;
import org.jeecg.modules.cw.mnlr.result.CwMnlrMonthQueryResult;
import org.jeecg.modules.cw.mnlr.result.CwMnlrStatisticsResult;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrDayService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import cn.hutool.core.date.DateUtil;

 /**
 * @Description: 矿模拟利润（日）
 * @Author: jeecg-boot
 * @Date:   2025-01-09
 * @Version: V1.0
 */
@Api(tags="矿模拟利润（日）")
@RestController
@RequestMapping("/mnlr/cwMnlrDay")
@Slf4j
public class CwMnlrDayController extends JeecgController<CwMnlrDay, ICwMnlrDayService> {
	@Autowired
	private ICwMnlrDayService cwMnlrDayService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwMnlrDay
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "矿模拟利润（日）-分页列表查询")
	@ApiOperation(value="矿模拟利润（日）-分页列表查询", notes="矿模拟利润（日）-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CwMnlrDay>> queryPageList(CwMnlrDay cwMnlrDay,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwMnlrDay> queryWrapper = QueryGenerator.initQueryWrapper(cwMnlrDay, req.getParameterMap());
		Page<CwMnlrDay> page = new Page<CwMnlrDay>(pageNo, pageSize);
		IPage<CwMnlrDay> pageList = cwMnlrDayService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwMnlrDay
	 * @return
	 */
	@AutoLog(value = "矿模拟利润（日）-添加")
	@ApiOperation(value="矿模拟利润（日）-添加", notes="矿模拟利润（日）-添加")
	@RequiresPermissions("mnlr:cw_mnlr_day:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwMnlrDay cwMnlrDay) {
		cwMnlrDayService.save(cwMnlrDay);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwMnlrDay
	 * @return
	 */
	@AutoLog(value = "矿模拟利润（日）-编辑")
	@ApiOperation(value="矿模拟利润（日）-编辑", notes="矿模拟利润（日）-编辑")
	@RequiresPermissions("mnlr:cw_mnlr_day:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwMnlrDay cwMnlrDay) {
		cwMnlrDayService.updateById(cwMnlrDay);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "矿模拟利润（日）-通过id删除")
	@ApiOperation(value="矿模拟利润（日）-通过id删除", notes="矿模拟利润（日）-通过id删除")
	@RequiresPermissions("mnlr:cw_mnlr_day:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwMnlrDayService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "矿模拟利润（日）-批量删除")
	@ApiOperation(value="矿模拟利润（日）-批量删除", notes="矿模拟利润（日）-批量删除")
	@RequiresPermissions("mnlr:cw_mnlr_day:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwMnlrDayService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "矿模拟利润（日）-通过id查询")
	@ApiOperation(value="矿模拟利润（日）-通过id查询", notes="矿模拟利润（日）-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwMnlrDay> queryById(@RequestParam(name="id",required=true) String id) {
		CwMnlrDay cwMnlrDay = cwMnlrDayService.getById(id);
		if(cwMnlrDay==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwMnlrDay);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwMnlrDay
    */
    @RequiresPermissions("mnlr:cw_mnlr_day:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwMnlrDay cwMnlrDay) {
        return super.exportXls(request, cwMnlrDay, CwMnlrDay.class, "矿模拟利润（日）");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("mnlr:cw_mnlr_day:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwMnlrDay.class);
    }

	 @ApiOperation(value = "矿模拟利润（日）-列表查询", notes = "矿模拟利润（日）-列表查询")
	 @GetMapping(value = "/query")
	 public Result<CwMnlrDayQueryResult> query(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
		 CwMnlrDayQueryResult result = cwMnlrDayService.queryByDate(queryDate);
		 return Result.ok(result);
	 }

	 @ApiOperation(value = "矿模拟利润（日）-提交", notes = "矿模拟利润（日）-提交")
	 @PostMapping(value = "/submit")
	 public Result<String> submit(@RequestBody CwMnlrDaySumbitParam submitParam) {
		 cwMnlrDayService.submit(submitParam);
		 return Result.OK("提交成功");
	 }
	 
	 /**
	  * 计算模拟利润
	  *
	  * @param queryDate 查询日期
	  * @return 模拟利润
	  */
	 @ApiOperation(value = "矿模拟利润（日）-计算模拟利润", notes = "矿模拟利润（日）-计算模拟利润")
	 @GetMapping(value = "/calculateMnlr")
	 public Result<BigDecimal> calculateMnlr(@RequestParam @DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
		 BigDecimal mnlr = cwMnlrDayService.calculateMnlr(queryDate);
		 return Result.OK(mnlr);
	 }
	 
	 /**
	  * 计算计划比
	  *
	  * @param queryDate 查询日期
	  * @return 计划比
	  */
	 @ApiOperation(value = "矿模拟利润（日）-计算计划比", notes = "矿模拟利润（日）-计算计划比")
	 @GetMapping(value = "/calculateJhb")
	 public Result<BigDecimal> calculateJhb(@RequestParam @DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
		 BigDecimal jhb = cwMnlrDayService.calculateJhb(queryDate);
		 return Result.OK(jhb);
	 }
	 
	 /**
	  * 计算所有统计值
	  *
	  * @param queryDate 查询日期
	  * @return 所有统计值
	  */
	 @ApiOperation(value = "矿模拟利润（日）-计算所有统计值", notes = "矿模拟利润（日）-计算所有统计值")
	 @GetMapping(value = "/calculateStatistics")
	 public Result<Map<String, BigDecimal>> calculateStatistics(@RequestParam @DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
		 Map<String, BigDecimal> statistics = cwMnlrDayService.calculateAllStatistics(queryDate);
		 return Result.OK(statistics);
	 }
	 
	 /**
	  * 计算所有统计值 (统一结果对象)
	  *
	  * @param queryDate 查询日期
	  * @return 统计结果对象
	  */
	 @ApiOperation(value = "矿模拟利润（日）-计算所有统计值(统一结果对象)", notes = "矿模拟利润（日）-计算所有统计值(统一结果对象)")
	 @GetMapping(value = "/calculateAllStatistics")
	 public Result<CwMnlrStatisticsResult> calculateAllStatistics(@RequestParam @DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
		 CwMnlrStatisticsResult statistics = cwMnlrDayService.calculateStatistics(queryDate);
		 return Result.OK(statistics);
	 }

    @ApiOperation(value = "矿模拟利润（日）-自动填充", notes = "先同步拉取当天数据，再异步补齐当月缺口")
    @GetMapping(value = "/autoFill")
    public Result<CwMnlrDayQueryResult> autoFill(@RequestParam @DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        return Result.OK(cwMnlrDayService.autoFill(queryDate));
    }
}
