package org.jeecg.modules.cw.jh.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.cw.jh.param.CwJhZhcbSumbitParam;
import org.jeecg.modules.cw.jh.result.CwJhZhcbListResult;
import org.jeecg.modules.cw.jh.service.ICwJhZhcbService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Description: 检化中心-综合成本表
 * @Author: tang
 * @Date: 2024-12-06
 * @Version: V1.0
 */
@Api(tags = "检化中心-综合财报")
@RestController
@RequestMapping("/jh/zhcb")
@Slf4j
public class CwJhZhcbController {

    @Resource
    private ICwJhZhcbService jhZhcbService;


    @ApiOperation(value = "检化中心-综合成本-列表查询", notes = "检化中心-综合成本-列表查询")
    @GetMapping(value = "/query")
    public Result<CwJhZhcbListResult> query(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        CwJhZhcbListResult result = jhZhcbService.query(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "检化中心-综合成本-自动填充", notes = "检化中心-综合成本-自动填充")
    @GetMapping(value = "/autoFill")
    public Result<CwJhZhcbListResult> autoFill(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        CwJhZhcbListResult result = jhZhcbService.autoFill(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "检化中心-综合成本-提交", notes = "检化中心-综合成本-提交")
    @PostMapping(value = "/submit")
    public Result<String> submit(@RequestBody CwJhZhcbSumbitParam submitParam) {
        jhZhcbService.submit(submitParam);
        return Result.OK("提交成功");
    }
}
