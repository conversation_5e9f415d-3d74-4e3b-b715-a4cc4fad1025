package org.jeecg.modules.cw.frdw.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.cw.frdw.entity.CwFrdwDay;
import org.jeecg.modules.cw.frdw.service.ICwFrdwDayService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 法人单位-日填报
 * @Author: jeecg-boot
 * @Date:   2024-12-31
 * @Version: V1.0
 */
@Api(tags="法人单位-日填报")
@RestController
@RequestMapping("/frdw/cwFrdwDay")
@Slf4j
public class CwFrdwDayController extends JeecgController<CwFrdwDay, ICwFrdwDayService> {
	@Autowired
	private ICwFrdwDayService cwFrdwDayService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwFrdwDay
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "法人单位-日填报-分页列表查询")
	@ApiOperation(value="法人单位-日填报-分页列表查询", notes="法人单位-日填报-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CwFrdwDay>> queryPageList(CwFrdwDay cwFrdwDay,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwFrdwDay> queryWrapper = QueryGenerator.initQueryWrapper(cwFrdwDay, req.getParameterMap());
		Page<CwFrdwDay> page = new Page<CwFrdwDay>(pageNo, pageSize);
		IPage<CwFrdwDay> pageList = cwFrdwDayService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwFrdwDay
	 * @return
	 */
	@AutoLog(value = "法人单位-日填报-添加")
	@ApiOperation(value="法人单位-日填报-添加", notes="法人单位-日填报-添加")
	@RequiresPermissions("frdw:cw_frdw_day:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwFrdwDay cwFrdwDay) {
		cwFrdwDayService.save(cwFrdwDay);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwFrdwDay
	 * @return
	 */
	@AutoLog(value = "法人单位-日填报-编辑")
	@ApiOperation(value="法人单位-日填报-编辑", notes="法人单位-日填报-编辑")
	@RequiresPermissions("frdw:cw_frdw_day:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwFrdwDay cwFrdwDay) {
		cwFrdwDayService.updateById(cwFrdwDay);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "法人单位-日填报-通过id删除")
	@ApiOperation(value="法人单位-日填报-通过id删除", notes="法人单位-日填报-通过id删除")
	@RequiresPermissions("frdw:cw_frdw_day:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwFrdwDayService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "法人单位-日填报-批量删除")
	@ApiOperation(value="法人单位-日填报-批量删除", notes="法人单位-日填报-批量删除")
	@RequiresPermissions("frdw:cw_frdw_day:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwFrdwDayService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "法人单位-日填报-通过id查询")
	@ApiOperation(value="法人单位-日填报-通过id查询", notes="法人单位-日填报-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwFrdwDay> queryById(@RequestParam(name="id",required=true) String id) {
		CwFrdwDay cwFrdwDay = cwFrdwDayService.getById(id);
		if(cwFrdwDay==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwFrdwDay);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwFrdwDay
    */
    @RequiresPermissions("frdw:cw_frdw_day:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwFrdwDay cwFrdwDay) {
        return super.exportXls(request, cwFrdwDay, CwFrdwDay.class, "法人单位-日填报");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("frdw:cw_frdw_day:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwFrdwDay.class);
    }

}
