package org.jeecg.modules.cw.statistics.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "MetalPriceResult", description = "金属价格返回对象")
public class CwMetalPriceResult {

    @ApiModelProperty(value = "日期，格式yyyy-MM-dd")
    private String date;

    @ApiModelProperty(value = "铜价")
    private BigDecimal cu;

    @ApiModelProperty(value = "金价")
    private BigDecimal au;

    @ApiModelProperty(value = "银价")
    private BigDecimal ag;

    @ApiModelProperty(value = "钼产品价格")
    private BigDecimal mo;

    @ApiModelProperty(value = "硫精矿价格")
    private BigDecimal ljk;
} 