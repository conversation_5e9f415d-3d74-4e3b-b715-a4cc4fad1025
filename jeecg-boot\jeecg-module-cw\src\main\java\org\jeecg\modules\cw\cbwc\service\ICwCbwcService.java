package org.jeecg.modules.cw.cbwc.service;

import org.jeecg.modules.cw.cbwc.entity.CwCbwc;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.cw.cbwc.param.CwCbwcSumbitParam;
import org.jeecg.modules.cw.cbwc.result.CwCbwcQueryResult;

import java.util.Date;

/**
 * @Description: 成本完成表
 * @Author: jeecg-boot
 * @Date:   2024-12-31
 * @Version: V1.0
 */
public interface ICwCbwcService extends IService<CwCbwc> {

    CwCbwcQueryResult queryByDate(Date queryDate);

    void submit(CwCbwcSumbitParam submitParam);

    CwCbwcQueryResult autoFill(Date queryDate);
}
