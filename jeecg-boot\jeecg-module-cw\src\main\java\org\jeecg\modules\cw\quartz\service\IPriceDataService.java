package org.jeecg.modules.cw.quartz.service;

import java.util.Date;

/**
 * 价格数据服务接口
 */
public interface IPriceDataService {
    
    /**
     * 获取价格数据
     * @param date 日期
     */
    void getPriceData(Date date);
    
    /**
     * 获取产量数据并更新
     * @param date 日期
     */
    void getOutputDataAndUpdate(Date date);
    
    /**
     * 获取历史价格数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    void getHistoryPriceData(Date startDate, Date endDate);
    
    /**
     * 获取历史产量数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    void getHistoryOutputData(Date startDate, Date endDate);
} 