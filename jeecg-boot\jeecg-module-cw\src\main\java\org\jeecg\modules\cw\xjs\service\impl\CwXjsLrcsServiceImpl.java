package org.jeecg.modules.cw.xjs.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.xjs.entity.*;
import org.jeecg.modules.cw.xjs.mapper.CwXjsLrcsMapper;
import org.jeecg.modules.cw.xjs.param.CwXjsLrcsSumbitParam;
import org.jeecg.modules.cw.xjs.param.CwXjsZhcbSumbitParam;
import org.jeecg.modules.cw.xjs.result.CwXjsLrcsListResult;
import org.jeecg.modules.cw.xjs.result.CwXjsZhcbListResult;
import org.jeecg.modules.cw.xjs.service.ICwXjsLrcsService;
import org.jeecg.modules.cw.xjs.service.ICwXjsLrcsXsDayService;
import org.jeecg.modules.cw.xjs.service.ICwXjsLrcsXsMonthService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 新技术利润测算
 * @Author: jeecg-boot
 * @Date:   2025-04-08
 * @Version: V1.0
 */
@Service
public class CwXjsLrcsServiceImpl extends ServiceImpl<CwXjsLrcsMapper, CwXjsLrcs> implements ICwXjsLrcsService {

    private static final String DICT_TYPE = "xjs_lrcs";
    private static final String DICT_TYPE_XS = "xjs_lrcs_xs";

    @Resource
    private ICwNameDictService nameDictService;
    @Resource
    private ICwXjsLrcsXsMonthService xjsLrcsXsMonthService;
    @Resource
    private ICwXjsLrcsXsDayService xjsLrcsXsDayService;

    @Override
    public CwXjsLrcsListResult query(Date queryDate) {
        CwXjsLrcsListResult result = new CwXjsLrcsListResult();
        result.setQueryDate(queryDate);
        // 项目字典
        List<CwNameDict> dict = nameDictService.queryList(DICT_TYPE);
        // 已有列表
        List<CwXjsLrcs> allData = this.lambdaQuery()
                .between(CwXjsLrcs::getRecordTime, DateUtil.beginOfDay(queryDate), DateUtil.endOfDay(queryDate))
                .list();
        // 合并数据
        List<CwXjsLrcsRow> resRows = new ArrayList<>();
        for (CwNameDict d : dict) {
            CwXjsLrcsRow row = new CwXjsLrcsRow();
            BeanUtil.copyProperties(d, row);
            // 数据
            allData.stream().filter(d1 -> d.getName().equals(d1.getName()))
                    .findFirst()
                    .ifPresent(d1 -> {
                        BeanUtils.copyProperties(d1, row);
                    });
            resRows.add(row);
        }
        result.setRows(resRows);
        // 项目字典
        List<CwNameDict> xsDict = nameDictService.queryList(DICT_TYPE_XS);
        List<CwXjsLrcsXsMonth> xsMonth = xjsLrcsXsMonthService.lambdaQuery()
                .between(CwXjsLrcsXsMonth::getRecordTime, DateUtil.beginOfMonth(queryDate), DateUtil.endOfMonth(queryDate))
                .list();
        List<CwXjsLrcsXsDay> xsDays = xjsLrcsXsDayService.lambdaQuery()
                .between(CwXjsLrcsXsDay::getRecordTime, DateUtil.beginOfDay(queryDate), DateUtil.endOfDay(queryDate))
                .list();
        // 合并数据
        List<CwXjsLrcsXsRow> resXsRows = new ArrayList<>();
        for (CwNameDict d : xsDict) {
            CwXjsLrcsXsRow row = new CwXjsLrcsXsRow();
            BeanUtil.copyProperties(d, row);
            // 数据
            xsMonth.stream().filter(d1 -> d.getName().equals(d1.getName()))
                    .findFirst()
                    .ifPresent(d1 -> {
                        BeanUtils.copyProperties(d1, row);
                    });
            xsDays.stream().filter(d1 -> d.getName().equals(d1.getName()))
                    .findFirst()
                    .ifPresent(d1 -> {
                        BeanUtils.copyProperties(d1, row);
                    });
            resXsRows.add(row);
        }
        result.setXsRows(resXsRows);
        // 结果
        return result;
    }
    @Override
    public void submit(CwXjsLrcsSumbitParam param) {
        Date submitDate = param.getSubmitDate();
        // 更新行数据
        List<CwXjsLrcsRow> rows = param.getRows();
        this.remove(new LambdaQueryWrapper<>(CwXjsLrcs.class)
                .between(CwXjsLrcs::getRecordTime, DateUtil.beginOfDay(submitDate), DateUtil.endOfDay(submitDate)));
        ArrayList<CwXjsLrcs> days = new ArrayList<>();
        for (CwXjsLrcsRow row : rows) {
            CwXjsLrcs day = new CwXjsLrcs();
            BeanUtil.copyProperties(row, day);
            day.setRecordTime(submitDate);
            days.add(day);
        }
        this.saveBatch(days);
        // 更新系数数据
        List<CwXjsLrcsXsRow> xsRows = param.getXsRows();
        xjsLrcsXsDayService.remove(new LambdaQueryWrapper<>(CwXjsLrcsXsDay.class)
                .between(CwXjsLrcsXsDay::getRecordTime, DateUtil.beginOfDay(submitDate), DateUtil.endOfDay(submitDate)));
        ArrayList<CwXjsLrcsXsDay> xsDays = new ArrayList<>();
        for (CwXjsLrcsXsRow row : xsRows) {
            CwXjsLrcsXsDay day = new CwXjsLrcsXsDay();
            BeanUtil.copyProperties(row, day);
            day.setRecordTime(submitDate);
            xsDays.add(day);
        }
        xjsLrcsXsDayService.saveBatch(xsDays);

        xjsLrcsXsMonthService.remove(new LambdaQueryWrapper<>(CwXjsLrcsXsMonth.class)
                .between(CwXjsLrcsXsMonth::getRecordTime, DateUtil.beginOfMonth(submitDate), DateUtil.endOfMonth(submitDate)));
        ArrayList<CwXjsLrcsXsMonth> xsMonths = new ArrayList<>();
        for (CwXjsLrcsXsRow row : xsRows) {
            CwXjsLrcsXsMonth month = new CwXjsLrcsXsMonth();
            BeanUtil.copyProperties(row, month);
            month.setRecordTime(submitDate);
            xsMonths.add(month);
        }
        xjsLrcsXsMonthService.saveBatch(xsMonths);

    }
}
