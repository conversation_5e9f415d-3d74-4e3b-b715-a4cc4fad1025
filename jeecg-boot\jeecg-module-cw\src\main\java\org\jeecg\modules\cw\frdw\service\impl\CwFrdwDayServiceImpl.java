package org.jeecg.modules.cw.frdw.service.impl;

import cn.hutool.core.date.DateUtil;
import org.jeecg.modules.cw.frdw.entity.CwFrdwDay;
import org.jeecg.modules.cw.frdw.mapper.CwFrdwDayMapper;
import org.jeecg.modules.cw.frdw.service.ICwFrdwDayService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @Description: 法人单位-日填报
 * @Author: jeecg-boot
 * @Date:   2024-12-31
 * @Version: V1.0
 */
@Service
public class CwFrdwDayServiceImpl extends ServiceImpl<CwFrdwDayMapper, CwFrdwDay> implements ICwFrdwDayService {

    @Override
    public List<CwFrdwDay> autoFill(Date queryDate) {
        // 从昨天开始，到本月1号为止，倒序查找可复制的数据源
        Date firstDayOfMonth = DateUtil.beginOfMonth(queryDate);
        for (Date dateToTry = DateUtil.offsetDay(queryDate, -1); !dateToTry.before(firstDayOfMonth); dateToTry = DateUtil.offsetDay(dateToTry, -1)) {
            List<CwFrdwDay> days = this.lambdaQuery()
                    .between(CwFrdwDay::getRecordTime, DateUtil.beginOfDay(dateToTry), DateUtil.endOfDay(dateToTry))
                    .list();
            if (!days.isEmpty()) {
                // 找到数据，立即使用这个日期进行查询并返回
                return days;
            }
        }
        // 如果本月（从昨天到1号）没有任何数据，则默认查询当天
        return Collections.emptyList();
    }
}
