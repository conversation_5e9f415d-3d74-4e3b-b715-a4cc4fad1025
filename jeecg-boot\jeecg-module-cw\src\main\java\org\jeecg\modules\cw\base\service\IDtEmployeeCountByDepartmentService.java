package org.jeecg.modules.cw.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.cw.base.entity.DtEmployeeCountByDepartment;

import java.util.Date;

/**
 * Service interface for 德铜人员统计表
 */
public interface IDtEmployeeCountByDepartmentService extends IService<DtEmployeeCountByDepartment> {

    /**
     * 获取指定时间范围内的人员总数
     *
     * @param startDate 开始日期（含）
     * @param endDate   结束日期（含）
     * @return 总人数
     */
    Integer getTotalEmployeeCount(Date startDate, Date endDate);

    /**
     * 获取指定月份的人员总数（排除特定单位：新技术、铸造、实业、化工、铜兴监测）
     * 取当月任意一天的人数总和（优先1号，没有就取2号，以此类推）
     *
     * @param startDate 月份开始日期
     * @param endDate   月份结束日期
     * @return 过滤后的人员总数
     */
    Integer getFilteredEmployeeCountForMonth(Date startDate, Date endDate);

    /**
     * 获取指定年份的人员平均数（排除特定单位：新技术、铸造、实业、化工、铜兴监测）
     * 取整年所有有效数据的平均值
     *
     * @param startDate 年份开始日期
     * @param endDate   年份结束日期
     * @return 过滤后的平均人员数
     */
    Integer getFilteredEmployeeCountForYear(Date startDate, Date endDate);
} 