package org.jeecg.modules.cw.quartz;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.cw.ckc.service.ICwCkcZhcbService;
import org.jeecg.modules.cw.ds.service.ICwDsZhcbService;
import org.jeecg.modules.cw.jw.service.ICwJwZhcbService;
import org.jeecg.modules.cw.sx.service.ICwSxZhcbService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> Assistant
 * @version 1.0
 * @description 定时任务：每日重新计算当日的drs
 * @date 2024-07-30
 */
@Slf4j
@Component
public class DrsTodayRecalculateJob implements Job {

    @Resource
    private ICwCkcZhcbService ckcZhcbService;

    @Resource
    private ICwDsZhcbService dsZhcbService;

    @Resource
    private ICwSxZhcbService sxZhcbService;

    @Resource
    private ICwJwZhcbService jwZhcbService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("【定时任务】开始重新计算当日数（drs）...");

        try {
            log.info("【定时任务】开始计算采矿场（ckc）当日数...");
            ckcZhcbService.recalculateTodayDrs();
            log.info("【定时任务】采矿场（ckc）当日数计算完成。");
        } catch (Exception e) {
            log.error("【定时任务】计算采矿场（ckc）当日数失败", e);
        }

        try {
            log.info("【定时任务】开始计算大山（ds）当日数...");
            dsZhcbService.recalculateTodayDrs();
            log.info("【定时任务】大山（ds）当日数计算完成。");
        } catch (Exception e) {
            log.error("【定时任务】计算大山（ds）当日数失败", e);
        }

        try {
            log.info("【定时任务】开始计算泗选厂（sx）当日数...");
            sxZhcbService.recalculateTodayDrs();
            log.info("【定时任务】泗选厂（sx）当日数计算完成。");
        } catch (Exception e) {
            log.error("【定时任务】计算泗选厂（sx）当日数失败", e);
        }

        try {
            log.info("【定时任务】开始计算精尾厂（jw）当日数...");
            jwZhcbService.recalculateTodayDrs();
            log.info("【定时任务】精尾厂（jw）当日数计算完成。");
        } catch (Exception e) {
            log.error("【定时任务】计算精尾厂（jw）当日数失败", e);
        }
        
        log.info("【定时任务】重新计算当日数（drs）完成。");
    }
} 