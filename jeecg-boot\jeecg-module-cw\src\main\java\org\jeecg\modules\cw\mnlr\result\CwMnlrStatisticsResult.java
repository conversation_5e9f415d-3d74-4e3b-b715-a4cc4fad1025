package org.jeecg.modules.cw.mnlr.result;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 矿模拟利润统计结果
 */
@Data
public class CwMnlrStatisticsResult {
    /**
     * 查询日期
     */
    private Date recordTime;
    
    /**
     * 销售收入总和
     */
    private BigDecimal sl;
    
    /**
     * 成本总和
     */
    private BigDecimal cb;
    
    /**
     * 第三方利润（分入）
     */
    private BigDecimal djlr;
    
    /**
     * 其他费用
     */
    private BigDecimal qt;
    
    /**
     * 模拟利润
     */
    private BigDecimal mnlr;
    
    /**
     * 月度计划日均值
     */
    private BigDecimal gsjh;
    
    /**
     * 计划比
     */
    private BigDecimal jhb;
    
    /**
     * 单位
     */
    private String unit = "万元";
} 