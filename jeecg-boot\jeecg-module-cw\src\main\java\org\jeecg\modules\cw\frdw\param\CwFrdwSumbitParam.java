package org.jeecg.modules.cw.frdw.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.modules.cw.base.entity.CwDwBase;
import org.jeecg.modules.cw.ckc.entity.CwCkcRow;
import org.jeecg.modules.cw.frdw.entity.CwFrdwRow;

import java.util.Date;
import java.util.List;

@Data
public class CwFrdwSumbitParam {
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;
    private List<CwFrdwRow> rows;
}
