package org.jeecg.modules.cw.cbgc.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.cw.cbgc.entity.CwCbgc;
import org.jeecg.modules.cw.cbgc.service.ICwCbgcService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 成本构成
 * @Author: jeecg-boot
 * @Date:   2025-02-20
 * @Version: V1.0
 */
@Api(tags="成本构成")
@RestController
@RequestMapping("/cbgc/cwCbgc")
@Slf4j
public class CwCbgcController extends JeecgController<CwCbgc, ICwCbgcService> {
	@Autowired
	private ICwCbgcService cwCbgcService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwCbgc
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "成本构成-分页列表查询")
	@ApiOperation(value="成本构成-分页列表查询", notes="成本构成-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CwCbgc>> queryPageList(CwCbgc cwCbgc,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwCbgc> queryWrapper = QueryGenerator.initQueryWrapper(cwCbgc, req.getParameterMap());
		Page<CwCbgc> page = new Page<CwCbgc>(pageNo, pageSize);
		IPage<CwCbgc> pageList = cwCbgcService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwCbgc
	 * @return
	 */
	@AutoLog(value = "成本构成-添加")
	@ApiOperation(value="成本构成-添加", notes="成本构成-添加")
	@RequiresPermissions("cbgc:cw_cbgc:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwCbgc cwCbgc) {
		cwCbgcService.save(cwCbgc);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwCbgc
	 * @return
	 */
	@AutoLog(value = "成本构成-编辑")
	@ApiOperation(value="成本构成-编辑", notes="成本构成-编辑")
	@RequiresPermissions("cbgc:cw_cbgc:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwCbgc cwCbgc) {
		cwCbgcService.updateById(cwCbgc);
		return Result.OK("编辑成功!");
	}

	 /**
	  *  新增或编辑
	  *
	  * @param cwCbgc
	  * @return
	  */
	 @AutoLog(value = "成本构成-新增或编辑")
	 @ApiOperation(value="成本构成-新增或编辑", notes="成本构成-新增或编辑")
	 @RequestMapping(value = "/addOrEdit", method = {RequestMethod.PUT,RequestMethod.POST})
	 public Result<String> addOrEdit(@RequestBody List<CwCbgc> cwCbgc) {
		 cwCbgcService.addOrEdit(cwCbgc);
		 return Result.OK("编辑成功!");
	 }
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "成本构成-通过id删除")
	@ApiOperation(value="成本构成-通过id删除", notes="成本构成-通过id删除")
	@RequiresPermissions("cbgc:cw_cbgc:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwCbgcService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "成本构成-批量删除")
	@ApiOperation(value="成本构成-批量删除", notes="成本构成-批量删除")
	@RequiresPermissions("cbgc:cw_cbgc:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwCbgcService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "成本构成-通过id查询")
	@ApiOperation(value="成本构成-通过id查询", notes="成本构成-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwCbgc> queryById(@RequestParam(name="id",required=true) String id) {
		CwCbgc cwCbgc = cwCbgcService.getById(id);
		if(cwCbgc==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwCbgc);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwCbgc
    */
    @RequiresPermissions("cbgc:cw_cbgc:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwCbgc cwCbgc) {
        return super.exportXls(request, cwCbgc, CwCbgc.class, "成本构成");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("cbgc:cw_cbgc:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwCbgc.class);
    }
}
