package org.jeecg.modules.cw.xjs.service;

import org.jeecg.modules.cw.xjs.entity.CwXjsLrcs;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.cw.xjs.param.CwXjsLrcsSumbitParam;
import org.jeecg.modules.cw.xjs.param.CwXjsZhcbSumbitParam;
import org.jeecg.modules.cw.xjs.result.CwXjsLrcsListResult;

import java.util.Date;

/**
 * @Description: 新技术利润测算
 * @Author: jeecg-boot
 * @Date:   2025-04-08
 * @Version: V1.0
 */
public interface ICwXjsLrcsService extends IService<CwXjsLrcs> {

    CwXjsLrcsListResult query(Date queryDate);

    void submit(CwXjsLrcsSumbitParam param);
}
