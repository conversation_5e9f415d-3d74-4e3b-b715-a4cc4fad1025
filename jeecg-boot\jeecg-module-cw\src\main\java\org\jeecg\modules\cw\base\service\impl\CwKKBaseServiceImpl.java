package org.jeecg.modules.cw.base.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import org.jeecg.modules.cw.base.entity.CwBaseData;
import org.jeecg.modules.cw.base.mapper.CwBaseDataMapper;
import org.jeecg.modules.cw.base.service.ICwKBaseService;
import org.jeecg.modules.cw.ckc.service.ICwCkcZhcbService;
import org.jeecg.modules.cw.ds.service.ICwDsZhcbService;
import org.jeecg.modules.cw.jg.service.ICwJgZhcbService;
import org.jeecg.modules.cw.jh.service.ICwJhZhcbService;
import org.jeecg.modules.cw.jw.service.ICwJwZhcbService;
import org.jeecg.modules.cw.krb.entity.CwKrbRow;
import org.jeecg.modules.cw.sx.service.ICwSxZhcbService;
import org.jetbrains.annotations.NotNull;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.jeecg.modules.cw.ckc.service.ICwCkcDayService;
import org.jeecg.modules.cw.ds.service.ICwDsDayService;
import org.jeecg.modules.cw.sx.service.ICwSxDayService;
import org.jeecg.modules.cw.jw.service.ICwJwDayService;
import org.jeecg.modules.cw.jh.service.ICwJhDayService;
import org.jeecg.modules.cw.jg.service.ICwJgDayService;
import org.jeecg.modules.cw.ckc.entity.CwCkcDay;
import org.jeecg.modules.cw.ds.entity.CwDsDay;
import org.jeecg.modules.cw.sx.entity.CwSxDay;
import org.jeecg.modules.cw.jw.entity.CwJwDay;
import org.jeecg.modules.cw.jh.entity.CwJhDay;
import org.jeecg.modules.cw.jg.entity.CwJgDay;

/**
 * @Description: 其他基础数据
 * @Author: jeecg-boot
 * @Date: 2025-01-08
 * @Version: V1.0
 */
@Service
public class CwKKBaseServiceImpl extends ServiceImpl<CwBaseDataMapper, CwBaseData> implements ICwKBaseService {

    @Resource
    private ICwCkcZhcbService ckcZhcbService;
    @Resource
    private ICwDsZhcbService dsZhcbService;
    @Resource
    private ICwSxZhcbService sxZhcbService;
    @Resource
    private ICwJwZhcbService jwZhcbService;
    @Resource
    private ICwJhZhcbService jhZhcbService;
    @Resource
    private ICwJgZhcbService jgZhcbService;
    @Resource
    private ICwCkcDayService ckcDayService;
    @Resource
    private ICwDsDayService dsDayService;
    @Resource
    private ICwSxDayService sxDayService;
    @Resource
    private ICwJwDayService jwDayService;
    @Resource
    private ICwJhDayService jhDayService;
    @Resource
    private ICwJgDayService jgDayService;

    public String getCwBaseDataYear(String name, Date queryDate) {
        return getCwBaseData(name, queryDate, DateField.YEAR);
    }

    public String getCwBaseDataMonth(String name, Date queryDate) {
        return getCwBaseData(name, queryDate, DateField.MONTH);
    }

    public String getCwBaseDataDay(String name, Date queryDate) {
        return getCwBaseData(name, queryDate, DateField.DAY_OF_YEAR);
    }

    public void setCwBaseDataYear(String name, String data, Date queryDate) {
        setCwBaseData(name, data, queryDate, DateField.YEAR);
    }

    public void setCwBaseDataMonth(String name, String data, Date queryDate) {
        setCwBaseData(name, data, queryDate, DateField.MONTH);
    }

    public void setCwBaseDataDay(String name, String data, Date queryDate) {
        setCwBaseData(name, data, queryDate, DateField.DAY_OF_YEAR);
    }

//    @Cacheable(cacheNames = "cwBaseData", key = "#name+#queryDate+#dateField")
    public String getCwBaseData(String name, Date queryDate, DateField dateField) {
        LambdaQueryChainWrapper<CwBaseData> wrapper = this.lambdaQuery().eq(CwBaseData::getName, name);
        if (DateField.YEAR.equals(dateField)) {
            wrapper.ge(CwBaseData::getRecordTime, DateUtil.beginOfYear(queryDate))
                    .le(CwBaseData::getRecordTime, DateUtil.endOfYear(queryDate));
        } else if (DateField.MONTH.equals(dateField)) {
            wrapper.ge(CwBaseData::getRecordTime, DateUtil.beginOfMonth(queryDate))
                    .le(CwBaseData::getRecordTime, DateUtil.endOfMonth(queryDate));
        } else if (DateField.DAY_OF_YEAR.equals(dateField)) {
            wrapper.ge(CwBaseData::getRecordTime, DateUtil.beginOfDay(queryDate))
                    .le(CwBaseData::getRecordTime, DateUtil.endOfDay(queryDate));
        }
        List<CwBaseData> list = wrapper.list();
        if (!list.isEmpty()) {
            return list.get(0).getData();
        } else {
            return null;
        }
    }

//    @CacheEvict(value = "cwBaseData", allEntries = true, condition = "#name+#queryDate+#dateField")
    public void setCwBaseData(String name, String data, Date queryDate, DateField dateField) {
        if (ObjectUtil.isEmpty(data)) {
            deleteCwBaseData(name, queryDate, dateField);
            return;
        }
        CwBaseData cwBaseData = new CwBaseData();
        cwBaseData.setName(name);
        cwBaseData.setData(data);
        cwBaseData.setRecordTime(queryDate);
        if (DateField.YEAR.equals(dateField)) {
            cwBaseData.setRecordTime(DateUtil.beginOfYear(queryDate));
            this.lambdaUpdate().eq(CwBaseData::getName, name)
                    .ge(CwBaseData::getRecordTime, DateUtil.beginOfYear(queryDate))
                    .le(CwBaseData::getRecordTime, DateUtil.endOfYear(queryDate))
                    .remove();
        } else if (DateField.MONTH.equals(dateField)) {
            cwBaseData.setRecordTime(DateUtil.beginOfMonth(queryDate));
            this.lambdaUpdate().eq(CwBaseData::getName, name)
                    .ge(CwBaseData::getRecordTime, DateUtil.beginOfMonth(queryDate))
                    .le(CwBaseData::getRecordTime, DateUtil.endOfMonth(queryDate))
                    .remove();
        } else if (DateField.DAY_OF_YEAR.equals(dateField)) {
            cwBaseData.setRecordTime(DateUtil.beginOfDay(queryDate));
            this.lambdaUpdate().eq(CwBaseData::getName, name)
                    .ge(CwBaseData::getRecordTime, DateUtil.beginOfDay(queryDate))
                    .le(CwBaseData::getRecordTime, DateUtil.endOfDay(queryDate))
                    .remove();
        }
        this.save(cwBaseData);
    }

    // 删除缓存
    public void deleteCwBaseData(String name, Date queryDate, DateField dateField) {
        if (DateField.YEAR.equals(dateField)) {
            this.lambdaUpdate().eq(CwBaseData::getName, name)
                    .ge(CwBaseData::getRecordTime, DateUtil.beginOfYear(queryDate))
                    .le(CwBaseData::getRecordTime, DateUtil.endOfYear(queryDate))
                    .remove();
        } else if (DateField.MONTH.equals(dateField)) {
            this.lambdaUpdate().eq(CwBaseData::getName, name)
                    .ge(CwBaseData::getRecordTime, DateUtil.beginOfMonth(queryDate))
                    .le(CwBaseData::getRecordTime, DateUtil.endOfMonth(queryDate))
                    .remove();
        } else if (DateField.DAY_OF_YEAR.equals(dateField)) {
            this.lambdaUpdate().eq(CwBaseData::getName, name)
                    .ge(CwBaseData::getRecordTime, DateUtil.beginOfDay(queryDate))
                    .le(CwBaseData::getRecordTime, DateUtil.endOfDay(queryDate))
                    .remove();
        }
    }

    /**
     * 矿成本（1号到当前天的月总计）
     */
    @NotNull
    public BigDecimal getKzcb(Date queryDate) {
        // 矿成本日报
        List<CwKrbRow> ckcRows = ckcZhcbService.sumByMonth(queryDate);
        List<CwKrbRow> dsRows = dsZhcbService.sumByMonth(queryDate);
        List<CwKrbRow> sxRows = sxZhcbService.sumByMonth(queryDate);
        List<CwKrbRow> jwRows = jwZhcbService.sumByMonth(queryDate);
        List<CwKrbRow> jhRows = jhZhcbService.sumByMonth(queryDate);
        List<CwKrbRow> jgRows = jgZhcbService.sumByMonth(queryDate);
        return Stream.of(ckcRows, dsRows, sxRows, jwRows, jhRows, jgRows).flatMap(List::stream).collect(Collectors.toList())
                .stream().map(CwKrbRow::getRlj).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    private BigDecimal sumDrsByMapper(BaseMapper<?> mapper, Date begin, Date end) {
        QueryWrapper<?> qw = new QueryWrapper<>()
                .select("COALESCE(SUM(drs),0) as drs")
                .ge("record_time", begin)
                .le("record_time", end);
        Object obj = ((BaseMapper) mapper).selectObjs(qw).stream().findFirst().orElse(BigDecimal.ZERO);
        return (obj instanceof BigDecimal) ? (BigDecimal) obj : new BigDecimal(obj.toString());
    }

    /**
     * 计算所有单位当日 drs 之和(当天的)
     */
    @Override
    public BigDecimal getTotalDrs(Date queryDate) {
        Date dayBegin = DateUtil.beginOfDay(queryDate);
        Date dayEnd = DateUtil.endOfDay(queryDate);

        BigDecimal ckc = sumDrsByMapper(ckcDayService.getBaseMapper(), dayBegin, dayEnd);
        BigDecimal ds = sumDrsByMapper(dsDayService.getBaseMapper(), dayBegin, dayEnd);
        BigDecimal sx = sumDrsByMapper(sxDayService.getBaseMapper(), dayBegin, dayEnd);
        BigDecimal jw = sumDrsByMapper(jwDayService.getBaseMapper(), dayBegin, dayEnd);
        BigDecimal jh = sumDrsByMapper(jhDayService.getBaseMapper(), dayBegin, dayEnd);
        BigDecimal jg = sumDrsByMapper(jgDayService.getBaseMapper(), dayBegin, dayEnd);

        return Stream.of(ckc, ds, sx, jw, jh, jg)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
