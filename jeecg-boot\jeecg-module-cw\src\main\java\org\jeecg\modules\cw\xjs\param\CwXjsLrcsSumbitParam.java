package org.jeecg.modules.cw.xjs.param;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.modules.cw.xjs.entity.CwXjsLrcsRow;
import org.jeecg.modules.cw.xjs.entity.CwXjsLrcsXsRow;
import org.jeecg.modules.cw.xjs.entity.CwXjsRow;

import java.util.Date;
import java.util.List;

@Data
public class CwXjsLrcsSumbitParam {
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;
    private List<CwXjsLrcsRow> rows;
    private List<CwXjsLrcsXsRow> xsRows;
}
