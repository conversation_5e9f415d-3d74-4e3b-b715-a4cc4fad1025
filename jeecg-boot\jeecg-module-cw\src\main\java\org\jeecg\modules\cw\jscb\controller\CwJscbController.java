package org.jeecg.modules.cw.jscb.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.cw.jscb.entity.CwJscb;
import org.jeecg.modules.cw.jscb.param.CwJscbSumbitParam;
import org.jeecg.modules.cw.jscb.result.CwJscbQueryResult;
import org.jeecg.modules.cw.jscb.service.ICwJscbService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 金属成本
 * @Author: jeecg-boot
 * @Date:   2025-01-15
 * @Version: V1.0
 */
@Api(tags="金属成本")
@RestController
@RequestMapping("/jscb/cwJscb")
@Slf4j
public class CwJscbController extends JeecgController<CwJscb, ICwJscbService> {
	@Autowired
	private ICwJscbService cwJscbService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwJscb
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "金属成本-分页列表查询")
	@ApiOperation(value="金属成本-分页列表查询", notes="金属成本-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CwJscb>> queryPageList(CwJscb cwJscb,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwJscb> queryWrapper = QueryGenerator.initQueryWrapper(cwJscb, req.getParameterMap());
		Page<CwJscb> page = new Page<CwJscb>(pageNo, pageSize);
		IPage<CwJscb> pageList = cwJscbService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwJscb
	 * @return
	 */
	@AutoLog(value = "金属成本-添加")
	@ApiOperation(value="金属成本-添加", notes="金属成本-添加")
	@RequiresPermissions("jscb:cw_jscb:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwJscb cwJscb) {
		cwJscbService.save(cwJscb);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwJscb
	 * @return
	 */
	@AutoLog(value = "金属成本-编辑")
	@ApiOperation(value="金属成本-编辑", notes="金属成本-编辑")
	@RequiresPermissions("jscb:cw_jscb:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwJscb cwJscb) {
		cwJscbService.updateById(cwJscb);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "金属成本-通过id删除")
	@ApiOperation(value="金属成本-通过id删除", notes="金属成本-通过id删除")
	@RequiresPermissions("jscb:cw_jscb:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwJscbService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "金属成本-批量删除")
	@ApiOperation(value="金属成本-批量删除", notes="金属成本-批量删除")
	@RequiresPermissions("jscb:cw_jscb:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwJscbService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "金属成本-通过id查询")
	@ApiOperation(value="金属成本-通过id查询", notes="金属成本-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwJscb> queryById(@RequestParam(name="id",required=true) String id) {
		CwJscb cwJscb = cwJscbService.getById(id);
		if(cwJscb==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwJscb);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwJscb
    */
    @RequiresPermissions("jscb:cw_jscb:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwJscb cwJscb) {
        return super.exportXls(request, cwJscb, CwJscb.class, "金属成本");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("jscb:cw_jscb:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwJscb.class);
    }

	 @ApiOperation(value = "金属成本表-列表查询", notes = "金属成本-列表查询")
	 @GetMapping(value = "/query")
	 public Result<CwJscbQueryResult> query(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
		 CwJscbQueryResult result = cwJscbService.queryByDate(queryDate);
		 return Result.ok(result);
	 }

	 @ApiOperation(value = "金属成本表-自动填充", notes = "金属成本-自动填充")
	 @GetMapping(value = "/autoFill")
	 public Result<CwJscbQueryResult> autoFill(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
		 CwJscbQueryResult result = cwJscbService.autoFill(queryDate);
		 return Result.ok(result);
	 }

	 @ApiOperation(value = "金属成本表-提交", notes = "金属成本-提交")
	 @PostMapping(value = "/submit")
	 public Result<String> submit(@RequestBody CwJscbSumbitParam submitParam) {
		 cwJscbService.submit(submitParam);
		 return Result.OK("提交成功");
	 }
}
