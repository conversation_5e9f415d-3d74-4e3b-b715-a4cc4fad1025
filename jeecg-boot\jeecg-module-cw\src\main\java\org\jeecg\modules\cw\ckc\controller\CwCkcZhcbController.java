package org.jeecg.modules.cw.ckc.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.cw.ckc.param.CwCkcZhcbSumbitParam;
import org.jeecg.modules.cw.ckc.result.CwCkcZhcbListResult;
import org.jeecg.modules.cw.ckc.service.ICwCkcZhcbService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Description: 采矿场-综合成本表
 * @Author: tang
 * @Date: 2024-12-06
 * @Version: V1.0
 */
@Api(tags = "采矿场-综合财报")
@RestController
@RequestMapping("/ckc/cwCkcZhcb")
@Slf4j
public class CwCkcZhcbController {

    @Resource
    private ICwCkcZhcbService ckcZhcbService;


    @ApiOperation(value = "采矿场-综合成本-列表查询", notes = "采矿场-综合成本-列表查询")
    @GetMapping(value = "/query")
    public Result<CwCkcZhcbListResult> query(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        CwCkcZhcbListResult result = ckcZhcbService.query(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "采矿场-综合成本-自动填充", notes = "采矿场-综合成本-自动填充")
    @GetMapping(value = "/autoFill")
    public Result<CwCkcZhcbListResult> autoFill(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        CwCkcZhcbListResult result = ckcZhcbService.autoFill(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "采矿场-综合成本-提交", notes = "采矿场-综合成本-提交")
    @PostMapping(value = "/submit")
    public Result<String> submit(@RequestBody CwCkcZhcbSumbitParam submitParam) {
        ckcZhcbService.submit(submitParam);
        return Result.OK("提交成功");
    }
}
