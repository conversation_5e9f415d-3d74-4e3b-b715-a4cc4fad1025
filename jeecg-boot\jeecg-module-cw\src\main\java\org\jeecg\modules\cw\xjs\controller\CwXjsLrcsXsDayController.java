package org.jeecg.modules.cw.xjs.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.cw.xjs.entity.CwXjsLrcsXsDay;
import org.jeecg.modules.cw.xjs.service.ICwXjsLrcsXsDayService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 新技术利润测算系数（日报）
 * @Author: jeecg-boot
 * @Date:   2025-04-09
 * @Version: V1.0
 */
@Api(tags="新技术利润测算系数（日报）")
@RestController
@RequestMapping("/xjs/cwXjsLrcsXsDay")
@Slf4j
public class CwXjsLrcsXsDayController extends JeecgController<CwXjsLrcsXsDay, ICwXjsLrcsXsDayService> {
	@Autowired
	private ICwXjsLrcsXsDayService cwXjsLrcsXsDayService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwXjsLrcsXsDay
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "新技术利润测算系数（日报）-分页列表查询")
	@ApiOperation(value="新技术利润测算系数（日报）-分页列表查询", notes="新技术利润测算系数（日报）-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CwXjsLrcsXsDay>> queryPageList(CwXjsLrcsXsDay cwXjsLrcsXsDay,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwXjsLrcsXsDay> queryWrapper = QueryGenerator.initQueryWrapper(cwXjsLrcsXsDay, req.getParameterMap());
		Page<CwXjsLrcsXsDay> page = new Page<CwXjsLrcsXsDay>(pageNo, pageSize);
		IPage<CwXjsLrcsXsDay> pageList = cwXjsLrcsXsDayService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwXjsLrcsXsDay
	 * @return
	 */
	@AutoLog(value = "新技术利润测算系数（日报）-添加")
	@ApiOperation(value="新技术利润测算系数（日报）-添加", notes="新技术利润测算系数（日报）-添加")
	@RequiresPermissions("xjs:cw_xjs_lrcs_xs_day:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwXjsLrcsXsDay cwXjsLrcsXsDay) {
		cwXjsLrcsXsDayService.save(cwXjsLrcsXsDay);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwXjsLrcsXsDay
	 * @return
	 */
	@AutoLog(value = "新技术利润测算系数（日报）-编辑")
	@ApiOperation(value="新技术利润测算系数（日报）-编辑", notes="新技术利润测算系数（日报）-编辑")
	@RequiresPermissions("xjs:cw_xjs_lrcs_xs_day:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwXjsLrcsXsDay cwXjsLrcsXsDay) {
		cwXjsLrcsXsDayService.updateById(cwXjsLrcsXsDay);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "新技术利润测算系数（日报）-通过id删除")
	@ApiOperation(value="新技术利润测算系数（日报）-通过id删除", notes="新技术利润测算系数（日报）-通过id删除")
	@RequiresPermissions("xjs:cw_xjs_lrcs_xs_day:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwXjsLrcsXsDayService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "新技术利润测算系数（日报）-批量删除")
	@ApiOperation(value="新技术利润测算系数（日报）-批量删除", notes="新技术利润测算系数（日报）-批量删除")
	@RequiresPermissions("xjs:cw_xjs_lrcs_xs_day:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwXjsLrcsXsDayService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "新技术利润测算系数（日报）-通过id查询")
	@ApiOperation(value="新技术利润测算系数（日报）-通过id查询", notes="新技术利润测算系数（日报）-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwXjsLrcsXsDay> queryById(@RequestParam(name="id",required=true) String id) {
		CwXjsLrcsXsDay cwXjsLrcsXsDay = cwXjsLrcsXsDayService.getById(id);
		if(cwXjsLrcsXsDay==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwXjsLrcsXsDay);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwXjsLrcsXsDay
    */
    @RequiresPermissions("xjs:cw_xjs_lrcs_xs_day:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwXjsLrcsXsDay cwXjsLrcsXsDay) {
        return super.exportXls(request, cwXjsLrcsXsDay, CwXjsLrcsXsDay.class, "新技术利润测算系数（日报）");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("xjs:cw_xjs_lrcs_xs_day:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwXjsLrcsXsDay.class);
    }

}
