<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.cw.ckc.mapper.CwCkcDayMapper">


    <select id="sum" resultType="java.math.BigDecimal">
        select sum(pjzh)
        from cw_ckc_day
        where record_time >= #{startDate}
          and record_time  <![CDATA[<=]]> #{endDate}
    </select>
    
    <select id="selectCwCkcDayList" resultType="org.jeecg.modules.cw.ckc.entity.CwCkcDay">
        select id, create_by, create_time, update_by, update_time, sys_org_code, 
               name, pjzh, record_time, type, unit, remark, drs
        from cw_ckc_day
        <where>
            <if test="startTime != null">
                and record_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and record_time <![CDATA[<=]]> #{endTime}
            </if>
        </where>
    </select>
</mapper>