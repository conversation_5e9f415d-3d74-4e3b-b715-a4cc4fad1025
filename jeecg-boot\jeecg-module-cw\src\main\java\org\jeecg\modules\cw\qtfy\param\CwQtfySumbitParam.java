package org.jeecg.modules.cw.qtfy.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.modules.cw.base.entity.CwDwBase;
import org.jeecg.modules.cw.ckc.entity.CwCkcRow;
import org.jeecg.modules.cw.qtfy.entity.CwQtfyRow;

import java.util.Date;
import java.util.List;

@Data
public class CwQtfySumbitParam {
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;
    private List<CwQtfyRow> rows;
}
