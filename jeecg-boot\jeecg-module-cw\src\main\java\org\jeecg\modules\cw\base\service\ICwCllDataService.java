package org.jeecg.modules.cw.base.service;

import org.jeecg.modules.cw.base.constants.CwCllDataName;
import org.jeecg.modules.cw.base.entity.CwCllData;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.Map;

/**
 * @Description: 处理量数据
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
public interface ICwCllDataService extends IService<CwCllData> {
    /**
     * 获取处理量数据
     * @param type 数据类型
     * @param recordTime 记录时间
     * @return 处理量数据
     */
    String getCllData(CwCllDataName type, Date recordTime);
    
    /**
     * 获取处理量数据（兼容旧版本，不推荐使用）
     * @param name 分类
     * @param recordTime 记录时间
     * @return 处理量数据
     * @deprecated 使用 {@link #getCllData(CwCllDataName, Date)} 代替
     */
    @Deprecated
    String getCllData(String name, Date recordTime);

    /**
     * 设置处理量数据
     * @param type 数据类型
     * @param data 数据
     * @param recordTime 记录时间
     */
    void setCllData(CwCllDataName type, String data, Date recordTime);
    
    /**
     * 设置处理量数据（兼容旧版本，不推荐使用）
     * @param name 分类
     * @param data 数据
     * @param recordTime 记录时间
     * @deprecated 使用 {@link #setCllData(CwCllDataName, String, Date)} 代替
     */
    @Deprecated
    void setCllData(String name, String data, Date recordTime);
    
    /**
     * 获取指定月份的所有处理量数据
     * @param type 数据类型
     * @param monthDate 月份日期（将自动转换为月初）
     * @return 日期到处理量数据的映射，key为日期字符串(yyyy-MM-dd)，value为数据
     */
    Map<String, String> getMonthCllData(CwCllDataName type, Date monthDate);
    
    /**
     * 获取指定月份的所有处理量数据（兼容旧版本，不推荐使用）
     * @param name 分类
     * @param monthDate 月份日期（将自动转换为月初）
     * @return 日期到处理量数据的映射，key为日期字符串(yyyy-MM-dd)，value为数据
     * @deprecated 使用 {@link #getMonthCllData(CwCllDataName, Date)} 代替
     */
    @Deprecated
    Map<String, String> getMonthCllData(String name, Date monthDate);
    
    /**
     * 获取日期范围内的处理量数据
     * @param type 数据类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期到处理量数据的映射，key为日期字符串(yyyy-MM-dd)，value为数据
     */
    Map<String, String> getRangeCllData(CwCllDataName type, Date startDate, Date endDate);
    
    /**
     * 获取日期范围内的处理量数据（兼容旧版本，不推荐使用）
     * @param name 分类
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期到处理量数据的映射，key为日期字符串(yyyy-MM-dd)，value为数据
     * @deprecated 使用 {@link #getRangeCllData(CwCllDataName, Date, Date)} 代替
     */
    @Deprecated
    Map<String, String> getRangeCllData(String name, Date startDate, Date endDate);
}
