package org.jeecg.modules.cw.frdw.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.ObjectUtils;
import org.jeecg.modules.cw.base.entity.CwDwBase;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwDwBaseService;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.frdw.entity.CwFrdwDay;
import org.jeecg.modules.cw.frdw.entity.CwFrdwYear;
import org.jeecg.modules.cw.frdw.entity.CwFrdwRow;
import org.jeecg.modules.cw.frdw.mapper.CwFrdwDayMapper;
import org.jeecg.modules.cw.frdw.param.CwFrdwSumbitParam;
import org.jeecg.modules.cw.frdw.result.CwFrdwListResult;
import org.jeecg.modules.cw.frdw.service.ICwFrdwDayService;
import org.jeecg.modules.cw.frdw.service.ICwFrdwYearService;
import org.jeecg.modules.cw.frdw.service.ICwFrdwService;
import org.jeecg.modules.cw.qtfy.entity.CwQtfy;
import org.jeecg.modules.cw.xjs.entity.CwXjsDay;
import org.jeecg.modules.cw.xjs.service.ICwXjsDayService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 采矿场-日填报
 * @Author: jeecg-boot
 * @Date: 2024-12-06
 * @Version: V1.0
 */
@Service
public class CwFrdwServiceImpl extends ServiceImpl<CwFrdwDayMapper, CwFrdwDay> implements ICwFrdwService {

    @Resource
    private ICwNameDictService nameDictService;
    @Resource
    private ICwFrdwDayService frdwDayService;
    @Resource
    private ICwFrdwYearService frdwYearService;
    @Resource
    private ICwXjsDayService xjsDayService;


    @Override
    public CwFrdwListResult query(Date queryDate) {
        CwFrdwListResult result = new CwFrdwListResult();
        result.setQueryDate(queryDate);
        // 项目字典
        List<CwNameDict> dict = nameDictService.queryList("frdw");
        // 已有列表
        List<CwFrdwDay> allDays = frdwDayService.lambdaQuery()
                .between(CwFrdwDay::getRecordTime, DateUtil.beginOfMonth(queryDate), DateUtil.endOfDay(queryDate))
                .list();
        List<CwFrdwDay> days = allDays.stream()
                .filter(d -> DateUtil.isSameDay(queryDate, d.getRecordTime()))
                .collect(Collectors.toList());
        List<CwFrdwYear> years = frdwYearService.lambdaQuery()
                .ge(CwFrdwYear::getRecordTime, DateUtil.beginOfYear(queryDate))
                .le(CwFrdwYear::getRecordTime, DateUtil.endOfYear(queryDate))
                .list();
        // 合并数据
        List<CwFrdwRow> resRows = new ArrayList<>();
        for (CwNameDict d : dict) {
            CwFrdwRow row = new CwFrdwRow();
            BeanUtil.copyProperties(d, row);
            // 数据
            days.stream().filter(d1 -> d.getName().equals(d1.getName()))
                    .findFirst()
                    .ifPresent(d1 -> {
                        row.setLrDrs(d1.getLrDrs());
                        row.setSlDrs(d1.getSlDrs());
                    });
            years.stream().filter(d1 -> d.getName().equals(d1.getName()))
                    .findFirst()
                    .ifPresent(d1 -> {
                        row.setJh(d1.getJh());
                    });
            // 新技术
            if ("xjs".equals(d.getType())) {
                List<CwXjsDay> xjsDays = xjsDayService.lambdaQuery()
                        .between(CwXjsDay::getRecordTime, DateUtil.beginOfMonth(queryDate), DateUtil.endOfDay(queryDate))
                        .list().stream()
                        .filter(x -> ObjectUtils.isNotEmpty(x.getDrs()))
                        .collect(Collectors.toList());
                // 利润累计
                BigDecimal lrLJ = xjsDays.stream()
                        .filter(x -> "lr".equals(x.getType()))
                        .map(CwXjsDay::getDrs)
                        .reduce(BigDecimal::add)
                        .orElse(BigDecimal.ZERO);
                row.setLrRlj(lrLJ);
                // 收入累计
                BigDecimal slLJ = xjsDays.stream()
                        .filter(x -> "sl".equals(x.getType()))
                        .map(CwXjsDay::getDrs)
                        .reduce(BigDecimal::add)
                        .orElse(BigDecimal.ZERO);
                row.setSlRlj(slLJ);

                List<CwXjsDay> curXjsDays = xjsDays.stream()
                        .filter(x -> DateUtil.isSameDay(queryDate, x.getRecordTime()))
                        .collect(Collectors.toList());
                // 利润当日数
                BigDecimal curLrDrs = curXjsDays.stream()
                        .filter(x -> "lr".equals(x.getType()))
                        .map(CwXjsDay::getDrs)
                        .reduce(BigDecimal::add)
                        .orElse(BigDecimal.ZERO);
                row.setLrDrs(curLrDrs);
                // 收入当日数
                BigDecimal curSlDrs = curXjsDays.stream()
                        .filter(x -> "sl".equals(x.getType()))
                        .map(CwXjsDay::getDrs)
                        .reduce(BigDecimal::add)
                        .orElse(BigDecimal.ZERO);
                row.setSlDrs(curSlDrs);
            } else {
                // 利润累计
                BigDecimal lrLJ = allDays.stream()
                        .filter(x -> d.getType().equals(x.getType()))
                        .map(CwFrdwDay::getLrDrs)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal::add)
                        .orElse(BigDecimal.ZERO);
                row.setLrRlj(lrLJ);
                // 收入累计
                BigDecimal slLJ = allDays.stream()
                        .filter(x -> d.getType().equals(x.getType()))
                        .map(CwFrdwDay::getSlDrs)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal::add)
                        .orElse(BigDecimal.ZERO);
                row.setSlRlj(slLJ);
            }

            resRows.add(row);
        }
        result.setRows(resRows);
        // 结果
        return result;
    }

    @Override
    public void submit(CwFrdwSumbitParam param) {
        Date submitDate = param.getSubmitDate();
        // 更新行数据
        List<CwFrdwRow> rows = param.getRows();
        frdwDayService.remove(new LambdaQueryWrapper<>(CwFrdwDay.class).eq(CwFrdwDay::getRecordTime, submitDate));
        frdwYearService.remove(new LambdaQueryWrapper<>(CwFrdwYear.class)
                .ge(CwFrdwYear::getRecordTime, DateUtil.beginOfYear(submitDate))
                .le(CwFrdwYear::getRecordTime, DateUtil.endOfYear(submitDate)));
        ArrayList<CwFrdwDay> days = new ArrayList<>();
        for (CwFrdwRow row : rows) {
            CwFrdwDay day = new CwFrdwDay();
            BeanUtil.copyProperties(row, day);
            day.setRecordTime(submitDate);
            days.add(day);
        }
        frdwDayService.saveBatch(days);
        ArrayList<CwFrdwYear> years = new ArrayList<>();
        for (CwFrdwRow row : rows) {
            CwFrdwYear month = new CwFrdwYear();
            BeanUtil.copyProperties(row, month);
            month.setRecordTime(submitDate);
            years.add(month);
        }
        frdwYearService.saveBatch(years);
    }

    @Override
    public BigDecimal sumMonth(Date queryDate) {
        BigDecimal sum = new BigDecimal(0);
        // 准备数据
        List<CwFrdwDay> allDays = frdwDayService.lambdaQuery()
                .between(CwFrdwDay::getRecordTime,
                        DateUtil.beginOfMonth(queryDate),
                        DateUtil.endOfMonth(queryDate))
                .notIn(CwFrdwDay::getType, "xjs")
                .list();
        // 计算
        for (CwFrdwDay d : allDays) {
            if (ObjectUtil.isNotEmpty(d.getLrDrs())) {
                sum = sum.add(d.getLrDrs());
            }
        }
        return sum;
    }

    @Override
    public BigDecimal sumDay(Date queryDate) {
        BigDecimal sum = new BigDecimal(0);
        // 准备数据
        List<CwFrdwDay> allDays = frdwDayService.lambdaQuery()
                .between(CwFrdwDay::getRecordTime, DateUtil.beginOfDay(queryDate), DateUtil.endOfDay(queryDate))
                .list();
        // 计算
        for (CwFrdwDay d : allDays) {
            if (ObjectUtil.isNotEmpty(d.getLrDrs())) {
                sum = sum.add(d.getLrDrs());
            }
        }
        return sum;
    }

    @Override
    public CwFrdwListResult autoFill(Date queryDate) {
        // 从昨天开始，到本月1号为止，倒序查找可复制的数据源
        Date firstDayOfMonth = DateUtil.beginOfMonth(queryDate);
        for (Date dateToTry = DateUtil.offsetDay(queryDate, -1); !dateToTry.before(firstDayOfMonth); dateToTry = DateUtil.offsetDay(dateToTry, -1)) {
            long count = frdwDayService.lambdaQuery()
                    .between(CwFrdwDay::getRecordTime, DateUtil.beginOfDay(dateToTry), DateUtil.endOfDay(dateToTry))
                    .count();
            if (count > 0) {
                // 找到数据，立即使用这个日期进行查询并返回
                return query(dateToTry);
            }
        }
        // 如果本月（从昨天到1号）没有任何数据，则默认查询当天
        return query(queryDate);
    }

    @Override
    public BigDecimal sumPeriod(Date startDate, Date endDate) {
        // 优化：一次性查询日期范围内的所有法人单位利润数据
        BigDecimal sum = BigDecimal.ZERO;
        
        // 直接查询日期范围内的所有数据
        List<CwFrdwDay> frdwDays = frdwDayService.lambdaQuery()
                .between(CwFrdwDay::getRecordTime, startDate, endDate)
                .list();
        
        // 累加利润值
        for (CwFrdwDay day : frdwDays) {
            if (ObjectUtil.isNotEmpty(day.getLrDrs())) {
                sum = sum.add(day.getLrDrs());
            }
        }
        
        return sum;
    }
}
