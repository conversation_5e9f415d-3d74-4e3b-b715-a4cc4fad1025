<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.cw.sx.mapper.CwSxDayMapper">


    <select id="sum" resultType="java.math.BigDecimal">
        select sum(pjzh)
        from cw_sx_day
        where record_time >= #{startDate}
          and record_time  <![CDATA[<=]]> #{endDate}
    </select>
</mapper>