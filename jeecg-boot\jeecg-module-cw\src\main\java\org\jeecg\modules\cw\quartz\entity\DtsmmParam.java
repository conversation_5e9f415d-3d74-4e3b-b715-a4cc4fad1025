package org.jeecg.modules.cw.quartz.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Data
@Builder
public class DtsmmParam {
    @JSONField(name = "report_date", format = "yyyy-MM-dd")
    private Date reportDate;
    @JSONField(name = "pageSize")
    private Integer pageSize;
    @JSONField(name = "offSet")
    private Integer offSet;
}
