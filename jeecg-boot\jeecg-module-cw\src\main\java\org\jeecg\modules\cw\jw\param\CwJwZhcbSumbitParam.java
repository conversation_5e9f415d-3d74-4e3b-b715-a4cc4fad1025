package org.jeecg.modules.cw.jw.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.modules.cw.jw.entity.CwJwRow;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CwJwZhcbSumbitParam {
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;
    private List<CwJwRow> rows;
    private BigDecimal jwCll;
    
    // 字段
    private BigDecimal cllyc; // 处理量预测
    private Boolean isFirstHalf; // 是否上半月
}
