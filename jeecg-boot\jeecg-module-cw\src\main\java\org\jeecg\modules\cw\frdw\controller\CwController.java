package org.jeecg.modules.cw.frdw.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.cw.frdw.param.CwFrdwSumbitParam;
import org.jeecg.modules.cw.frdw.result.CwFrdwListResult;
import org.jeecg.modules.cw.frdw.service.ICwFrdwService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Description: 法人单位-表
 * @Author: tang
 * @Date: 2024-12-06
 * @Version: V1.0
 */
@Api(tags = "法人单位-综合财报")
@RestController
@RequestMapping("/frdw")
@Slf4j
public class CwController {

    @Resource
    private ICwFrdwService frdwService;


    @ApiOperation(value = "法人单位-列表查询", notes = "法人单位-列表查询")
    @GetMapping(value = "/query")
    public Result<CwFrdwListResult> query(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        CwFrdwListResult result = frdwService.query(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "法人单位-自动填充", notes = "法人单位-自动填充")
    @GetMapping(value = "/autoFill")
    public Result<CwFrdwListResult> autoFill(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        CwFrdwListResult result = frdwService.autoFill(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "法人单位-提交", notes = "法人单位-提交")
    @PostMapping(value = "/submit")
    public Result<String> submit(@RequestBody CwFrdwSumbitParam submitParam) {
        frdwService.submit(submitParam);
        return Result.OK("提交成功");
    }
}
