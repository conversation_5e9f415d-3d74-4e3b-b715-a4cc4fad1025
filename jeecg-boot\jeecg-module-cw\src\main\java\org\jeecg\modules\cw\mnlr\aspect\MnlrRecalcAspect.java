package org.jeecg.modules.cw.mnlr.aspect;

import lombok.extern.log4j.Log4j2;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.jeecg.modules.cw.mnlr.param.CwMnlrDaySumbitParam;
import org.jeecg.modules.cw.mnlr.param.CwMnlrMonthSumbitParam;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrStatisticsDayService;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import javax.annotation.PreDestroy;

/**
 * 提交成功后自动重算当日模拟利润的切面。
 * <p>
 * 拦截 mnlr 包内 Service 的 *submit(..) 方法，
 * 根据参数中的 submitDate 或 Date 推断需要重算的日期，
 * 调用 {@link ICwMnlrStatisticsDayService#recalcDay(Date)} 进行重算。
 */
@Aspect
@Component
@Log4j2
public class MnlrRecalcAspect {

    @Resource
    private ICwMnlrStatisticsDayService statisticsDayService;

    /**
     * 单线程池，顺序执行重算任务，避免高并发写数据库导致锁冲突。
     */
    private final ExecutorService recalcExecutor = Executors.newSingleThreadExecutor(new ThreadFactory() {
        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, "mnlr-recalc-executor");
            t.setDaemon(true);
            return t;
        }
    });

    @PreDestroy
    public void shutdown() {
        recalcExecutor.shutdown();
    }

    /**
     * Pointcut 说明：
     *  - 任意返回值，位于 org.jeecg.modules.cw.mnlr.service 包及其子包
     *  - 方法名以 submit 结尾
     */
    private static final String POINTCUT = "execution(* org.jeecg.modules.cw.*.service..*.submit(..))";

    @AfterReturning(pointcut = POINTCUT)
    public void afterSubmit(JoinPoint joinPoint) {
        Date dateToRecalc = extractDate(joinPoint.getArgs());
        if (dateToRecalc != null) {
            try {
                log.info("[MnlrRecalcAspect] submit 完成后异步触发当日重算：{}", dateToRecalc);
                recalcExecutor.submit(() -> {
                    try {
                        statisticsDayService.recalcDay(dateToRecalc);
                    } catch (Exception ex) {
                        log.error("[MnlrRecalcAspect] 异步重算当天模拟利润异常", ex);
                    }
                });
            } catch (Exception e) {
                log.error("[MnlrRecalcAspect] 重算当天模拟利润异常", e);
            }
        } else {
            log.warn("[MnlrRecalcAspect] 未获取到 submitDate，跳过重算");
        }
    }

    /**
     * 从方法参数中提取日期信息。
     * 支持以下情况：
     *  1. 参数本身为 java.util.Date
     *  2. 参数为 CwMnlrDaySumbitParam / CwMnlrMonthSumbitParam，读取 getSubmitDate()
     */
    private Date extractDate(Object[] args) {
        if (args == null) {
            return null;
        }
        for (Object arg : args) {
            if (arg == null) {
                continue;
            }
            if (arg instanceof Date) {
                return (Date) arg;
            }
            if (arg instanceof CwMnlrDaySumbitParam) {
                return ((CwMnlrDaySumbitParam) arg).getSubmitDate();
            }
            if (arg instanceof CwMnlrMonthSumbitParam) {
                return ((CwMnlrMonthSumbitParam) arg).getSubmitDate();
            }
        }
        return null;
    }
} 