package org.jeecg.modules.cw.jscb.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.Setter;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwKBaseService;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.jlfx.entity.CwJlfxMonth;
import org.jeecg.modules.cw.jscb.entity.CwJsZcb;
import org.jeecg.modules.cw.jscb.entity.CwJscbRow;
import org.jeecg.modules.cw.jscb.entity.JscbConst;
import org.jeecg.modules.cw.jscb.param.CwJscbSumbitParam;
import org.jeecg.modules.cw.jscb.result.CwJscbQueryResult;
import org.jeecg.modules.cw.jscb.entity.CwJscb;
import org.jeecg.modules.cw.jscb.mapper.CwJscbMapper;
import org.jeecg.modules.cw.jscb.service.ICwJscbService;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrDay;
import org.jeecg.modules.cw.mnlr.mapper.CwMnlrDayMapper;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrDayService;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @Description: 金属成本
 * @Author: jeecg-boot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Service
public class CwJscbServiceImpl extends ServiceImpl<CwJscbMapper, CwJscb> implements ICwJscbService {
    private static final String DICT_TYPE = "jscb";

    @Resource
    private ICwNameDictService nameDictService;
    @Resource
    private ICwKBaseService cwKBaseService;
    @Resource
    private CwMnlrDayMapper mnlrDayMapper;

    @Override
    public CwJscbQueryResult queryByDate(Date queryDate) {
        CwJscbQueryResult result = new CwJscbQueryResult();
        result.setQueryDate(queryDate);
        // 项目字典
        List<CwNameDict> dict = nameDictService.queryList(DICT_TYPE);
        // 已有列表
        List<CwJscb> jscb = this.lambdaQuery()
                .between(CwJscb::getRecordTime, DateUtil.beginOfMonth(queryDate), DateUtil.endOfMonth(queryDate))
                .list();
        List<CwMnlrDay> mnlrDays = mnlrDayMapper.selectList(new LambdaQueryWrapper<CwMnlrDay>()
                .between(CwMnlrDay::getRecordTime, DateUtil.beginOfDay(queryDate), DateUtil.endOfDay(queryDate)));
        Map<String, CwMnlrDay> mnlrDayType = CollStreamUtil.toIdentityMap(mnlrDays, CwMnlrDay::getType);

        // 月累计金属量
        List<CwMnlrDay> mnlrMonthDays = mnlrDayMapper.selectList(new LambdaQueryWrapper<CwMnlrDay>()
                .between(CwMnlrDay::getRecordTime, DateUtil.beginOfMonth(queryDate), DateUtil.endOfDay(queryDate)));
        Map<String, BigDecimal> monthSumMap = new HashMap<>();
        for (CwMnlrDay day : mnlrMonthDays) {
            if (day.getType() == null || day.getXl() == null) {
                continue;
            }
            monthSumMap.merge(day.getType(), day.getXl(), BigDecimal::add);
        }

        // 特殊值映射，减少switch-case的使用
        Map<String, String> typeMapping = new HashMap<>();
        typeMapping.put("t", "t");
        typeMapping.put("tjk", "t");
        typeMapping.put("j", "j");
        typeMapping.put("y", "y");
        typeMapping.put("ljk", "ljk");
        
        // 合并数据
        List<CwJscbRow> resRows = new ArrayList<>();
        for (CwNameDict d : dict) {
            CwJscbRow row = new CwJscbRow();
            BeanUtil.copyProperties(d, row);
            
            // 合并已有数据
            jscb.stream()
                .filter(v -> d.getName().equals(v.getName()))
                .findFirst()
                .ifPresent(v -> BeanUtil.copyProperties(v, row));
            
            // 设置当日数据
            String type = row.getType();
            if (ObjectUtil.isNotEmpty(type)) {
                if ("mjk".equals(type)) {
                    row.setDrs(new BigDecimal(25));
                    // 钼精矿的累计量业务上按固定值处理
                    row.setLjs(new BigDecimal(25));
                } else {
                    String mappedType = typeMapping.get(type);
                    row.setDrs(mappedType != null ? getDrsValue(mnlrDayType, mappedType) : BigDecimal.ZERO);

                    // 设置累计金属量
                    BigDecimal ljs = mappedType != null ? monthSumMap.getOrDefault(mappedType, BigDecimal.ZERO) : BigDecimal.ZERO;
                    row.setLjs(ljs);
                }
            } else {
                row.setDrs(BigDecimal.ZERO);
                row.setLjs(BigDecimal.ZERO);
            }
            
            // 添加到结果集
            resRows.add(row);
        }
        
        result.setRows(resRows);
        
        // 设置基础数据
        result.setDyf(new BigDecimal(Optional.ofNullable(cwKBaseService.getCwBaseDataMonth(JscbConst.DYF, queryDate)).orElse("0")));
        result.setZycb(new BigDecimal(Optional.ofNullable(cwKBaseService.getCwBaseDataMonth(JscbConst.ZYCB, queryDate)).orElse("0")));
        result.setMsft(new BigDecimal(Optional.ofNullable(cwKBaseService.getCwBaseDataMonth(JscbConst.MSFT, queryDate)).orElse("0")));
        result.setClcb(new BigDecimal(Optional.ofNullable(cwKBaseService.getCwBaseDataMonth(JscbConst.CLCB, queryDate)).orElse("0")));
        BigDecimal zcb = cwKBaseService.getKzcb(queryDate);
        result.setZcb(zcb);
        
        return result;
    }


    @Override
    public CwJscbQueryResult autoFill(Date queryDate) {
        // 从昨天开始，到本月1号为止，倒序查找可复制的数据源
        Date firstDayOfMonth = DateUtil.beginOfMonth(queryDate);
        for (Date dateToTry = DateUtil.offsetDay(queryDate, -1); !dateToTry.before(firstDayOfMonth); dateToTry = DateUtil.offsetDay(dateToTry, -1)) {
            // 在金属成本这个特殊的业务中，我们是根据 mnlr_day 表来判断是否有数据的
            long count = mnlrDayMapper.selectCount(new LambdaQueryWrapper<CwMnlrDay>()
                    .between(CwMnlrDay::getRecordTime, DateUtil.beginOfDay(dateToTry), DateUtil.endOfDay(dateToTry)));
            if (count > 0) {
                // 找到数据，立即使用这个日期进行查询并返回
                return queryByDate(dateToTry);
            }
        }
        // 如果本月（从昨天到1号）没有任何数据，则默认查询当天
        return queryByDate(queryDate);
    }

    @Override
    // 月金属成本
    public List<CwJsZcb> getJscb(Date queryDate) {
        List<CwJsZcb> res = new ArrayList<>();
        // 基础数据
        BigDecimal dyf = new BigDecimal(Optional.ofNullable(cwKBaseService.getCwBaseDataMonth(JscbConst.DYF, queryDate)).orElse("0"));
        BigDecimal zycb = new BigDecimal(Optional.ofNullable(cwKBaseService.getCwBaseDataMonth(JscbConst.ZYCB, queryDate)).orElse("0"));
        BigDecimal msft = new BigDecimal(Optional.ofNullable(cwKBaseService.getCwBaseDataMonth(JscbConst.MSFT, queryDate)).orElse("0"));
        BigDecimal clcb = new BigDecimal(Optional.ofNullable(cwKBaseService.getCwBaseDataMonth(JscbConst.CLCB, queryDate)).orElse("0"));
        BigDecimal zcb = cwKBaseService.getKzcb(queryDate);
        // 金属数据
        List<CwJscb> jscb = this.lambdaQuery()
                .ge(CwJscb::getRecordTime, DateUtil.beginOfMonth(queryDate))
                .le(CwJscb::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();
        if (ObjectUtil.isEmpty(jscb)) {
            return res;
        }
        CwJscb t = jscb.stream()
                .filter((v) -> "t".equals(v.getType()))
                .findFirst()
                .orElse(new CwJscb());
        CwJscb j = jscb.stream()
                .filter((v) -> "j".equals(v.getType()))
                .findFirst()
                .orElse(new CwJscb());
        CwJscb y = jscb.stream()
                .filter((v) -> "y".equals(v.getType()))
                .findFirst()
                .orElse(new CwJscb());
        CwJscb tjk = jscb.stream()
                .filter((v) -> "tjk".equals(v.getType()))
                .findFirst()
                .orElse(new CwJscb());
        CwJscb ljk = jscb.stream()
                .filter((v) -> "ljk".equals(v.getType()))
                .findFirst()
                .orElse(new CwJscb());
        CwJscb mjk = jscb.stream()
                .filter((v) -> "mjk".equals(v.getType()))
                .findFirst()
                .orElse(new CwJscb());

        // 检查相关值是否为空
        if (t.getFtxs3() == null || j.getFtxs3() == null || y.getFtxs3() == null || 
            tjk.getFtxs1() == null || tjk.getFtxs2() == null || 
            ljk.getFtxs1() == null || ljk.getFtxs2() == null || 
            mjk.getFtxs1() == null) {
            return res;
        }

        // 计算数据
        BigDecimal zcbSub = zcb.subtract(dyf).subtract(zycb).subtract(msft).subtract(clcb);
        BigDecimal tjkCb = BigDecimal.ZERO;
        if (ObjectUtil.isNotNull(tjk.getFtxs1()) && ObjectUtil.isNotNull(tjk.getFtxs2())) {
            tjkCb = zcbSub
                    .multiply(tjk.getFtxs1().multiply(new BigDecimal("0.01")))
                    .add(zycb.multiply(tjk.getFtxs2().multiply(new BigDecimal("0.01"))))
                    .add(dyf)
                    .add(msft);
        } else {
            // 如果系数为空，直接返回空值
            res.add(new CwJsZcb("tjk", null));
            res.add(new CwJsZcb("t", null));
            res.add(new CwJsZcb("j", null));
            res.add(new CwJsZcb("y", null));
            res.add(new CwJsZcb("ljk", null));
            res.add(new CwJsZcb("mjk", null));
            return res;
        }
        
        BigDecimal tCb = null;
        if (ObjectUtil.isNotNull(t.getFtxs3())) {
            tCb = (tjkCb.subtract(msft)).multiply(t.getFtxs3().multiply(new BigDecimal("0.01")));
        }
        
        BigDecimal jCb = null;
        if (ObjectUtil.isNotNull(j.getFtxs3())) {
            jCb = ((tjkCb.subtract(msft)).multiply(j.getFtxs3().multiply(new BigDecimal("0.01")))).add(msft);
        }
        
        BigDecimal yCb = null;
        if (ObjectUtil.isNotNull(y.getFtxs3())) {
            yCb = (tjkCb.subtract(msft)).multiply(y.getFtxs3().multiply(new BigDecimal("0.01")));
        }
        
        BigDecimal ljkCb = null;
        if (ObjectUtil.isNotNull(ljk.getFtxs1()) && ObjectUtil.isNotNull(ljk.getFtxs2())) {
            ljkCb = zcbSub
                    .multiply(ljk.getFtxs1().multiply(new BigDecimal("0.01")))
                    .add(zycb.multiply(ljk.getFtxs2().multiply(new BigDecimal("0.01"))));
        }
        
        BigDecimal mjkCb = null;
        if (ObjectUtil.isNotNull(mjk.getFtxs1())) {
            mjkCb = zcbSub
                    .multiply(mjk.getFtxs1().multiply(new BigDecimal("0.01")))
                    .add(clcb);
        }
        // 组装
        res.add(new CwJsZcb("t", tCb));
        res.add(new CwJsZcb("j", jCb));
        res.add(new CwJsZcb("y", yCb));
        res.add(new CwJsZcb("tjk", tjkCb));
        res.add(new CwJsZcb("ljk", ljkCb));
        res.add(new CwJsZcb("mjk", mjkCb));

        return res;
    }


    @Override
    public void submit(CwJscbSumbitParam param) {
        Date submitDate = param.getSubmitDate();
        // 更新基本数据
        cwKBaseService.setCwBaseDataMonth(JscbConst.DYF, param.getDyf(), submitDate);
        cwKBaseService.setCwBaseDataMonth(JscbConst.ZYCB, param.getZycb(), submitDate);
        cwKBaseService.setCwBaseDataMonth(JscbConst.MSFT, param.getMsft(), submitDate);
        cwKBaseService.setCwBaseDataMonth(JscbConst.CLCB, param.getClcb(), submitDate);
        // 更新行数据
        List<CwJscbRow> rows = param.getRows();
        this.remove(new LambdaQueryWrapper<>(CwJscb.class)
                .ge(CwJscb::getRecordTime, DateUtil.beginOfMonth(submitDate))
                .le(CwJscb::getRecordTime, DateUtil.endOfMonth(submitDate)));
        ArrayList<CwJscb> months = new ArrayList<>();
        for (CwJscbRow row : rows) {
            CwJscb month = new CwJscb();
            BeanUtil.copyProperties(row, month);
            month.setRecordTime(submitDate);
            months.add(month);
        }
        this.saveBatch(months);
    }

    /**
     * 安全地从mnlrDayType获取xl值
     * 
     * @param mnlrDayType 数据Map
     * @param type 类型
     * @return 返回xl值，如果不存在则返回0
     */
    private BigDecimal getDrsValue(Map<String, CwMnlrDay> mnlrDayType, String type) {
        if (mnlrDayType != null && mnlrDayType.get(type) != null && mnlrDayType.get(type).getXl() != null) {
            return mnlrDayType.get(type).getXl();
        }
        return BigDecimal.ZERO;
    }
}
