package org.jeecg.modules.cw.mnlr.service;

import org.jeecg.modules.cw.mnlr.entity.CwMnlrDay;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.cw.mnlr.param.CwMnlrDaySumbitParam;
import org.jeecg.modules.cw.mnlr.param.CwMnlrMonthSumbitParam;
import org.jeecg.modules.cw.mnlr.result.CwMnlrDayQueryResult;
import org.jeecg.modules.cw.mnlr.result.CwMnlrMonthQueryResult;
import org.jeecg.modules.cw.mnlr.result.CwMnlrStatisticsResult;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * @Description: 矿模拟利润（日）
 * @Author: jeecg-boot
 * @Date:   2025-01-09
 * @Version: V1.0
 */
public interface ICwMnlrDayService extends IService<CwMnlrDay> {

    CwMnlrDayQueryResult queryByDate(Date queryDate);

    void submit(CwMnlrDaySumbitParam submitParam);
    
    /**
     * 计算模拟利润
     * @param queryDate 查询日期
     * @return 模拟利润值
     */
    BigDecimal calculateMnlr(Date queryDate);
    
    /**
     * 计算计划比（模拟利润与计划的比较差额）
     * @param queryDate 查询日期
     * @return 计划比
     */
    BigDecimal calculateJhb(Date queryDate);
    
    /**
     * 计算销售收入总和
     * @param queryDate 查询日期
     * @return 销售收入总和
     */
    BigDecimal calculateSl(Date queryDate);
    
    /**
     * 计算成本总和
     * @param queryDate 查询日期
     * @return 成本总和
     */
    BigDecimal calculateCb(Date queryDate);
    
    /**
     * 获取第三方利润（分入）
     * @param queryDate 查询日期
     * @return 第三方利润
     */
    BigDecimal getDjlr(Date queryDate);
    
    /**
     * 获取其他费用
     * @param queryDate 查询日期
     * @return 其他费用
     */
    BigDecimal getQt(Date queryDate);
    
    /**
     * 计算月度计划的日均值
     * @param queryDate 查询日期
     * @return 月度计划日均值
     */
    BigDecimal calculateGsjh(Date queryDate);
    
    /**
     * 计算所有统计值
     * @param queryDate 查询日期
     * @return 统计值的Map，包含sl, cb, djlr, qt, mnlr, gsjh, jhb
     */
    Map<String, BigDecimal> calculateAllStatistics(Date queryDate);
    
    /**
     * 计算所有统计值并返回统一结果对象
     * @param queryDate 查询日期
     * @return 统计结果对象
     */
    CwMnlrStatisticsResult calculateStatistics(Date queryDate);

    CwMnlrDayQueryResult autoFill(java.util.Date queryDate);
}
