package org.jeecg.modules.cw.statistics.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 统计实体（占位，用于保证 MyBatis-Plus 泛型完整性）
 * 若后续需要持久化更多统计数据，可在此补充字段
 * @Author: jeecg-boot
 */
@Data
@TableName("cw_statistics")
@Accessors(chain = true)
@ApiModel(value = "cw_statistics对象", description = "统计实体")
public class CwStatistics implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /** 统计日期 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "统计日期")
    private Date recordTime;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;
} 