package org.jeecg.modules.cw.mnlr.result;

import lombok.Data;
import org.jeecg.modules.cw.cbwc.entity.CwCbwcRow;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrMonthRow;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CwMnlrMonthQueryResult {
    private Date queryDate;
    private List<CwMnlrMonthRow> rows;
    private BigDecimal qtfy;
    private BigDecimal frdw;
    private BigDecimal jh;
    private BigDecimal tzlr;

    /**
     * 本月矿总成本
     */
    private BigDecimal zcb;
}
