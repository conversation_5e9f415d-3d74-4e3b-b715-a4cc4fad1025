package org.jeecg.modules.cw.sx.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.modules.cw.base.entity.CwDwBase;
import org.jeecg.modules.cw.ckc.entity.CwCkcRow;
import org.jeecg.modules.cw.sx.entity.CwSxRow;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CwSxZhcbSumbitParam {
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;
    private BaseData base;
    private List<CwSxRow> rows;

    /**
     * 是否为上半月预测: true 表示1-15号; false 表示16号-月底
     */
    private Boolean isFirstHalf;

    /**
     * 基础数据类
     */
    @Data
    public static class BaseData {
        /**处理量*/
        private BigDecimal sxCll;
        /**处理量预测*/
        private BigDecimal cllyc;
    }
}
