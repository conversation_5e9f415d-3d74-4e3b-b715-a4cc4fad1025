<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.cw.ds.mapper.CwDsDayMapper">


    <select id="sum" resultType="java.math.BigDecimal">
        select sum(pjzh)
        from cw_ds_day
        where record_time >= #{startDate}
          and record_time  <![CDATA[<=]]> #{endDate}
    </select>
</mapper>