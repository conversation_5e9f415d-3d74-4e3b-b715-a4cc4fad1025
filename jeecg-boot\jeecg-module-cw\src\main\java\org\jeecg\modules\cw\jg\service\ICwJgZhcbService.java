package org.jeecg.modules.cw.jg.service;



import org.jeecg.modules.cw.jg.param.CwJgZhcbSumbitParam;
import org.jeecg.modules.cw.jg.result.CwJgZhcbListResult;
import org.jeecg.modules.cw.krb.entity.CwKrbRow;

import java.util.Date;
import java.util.List;

public interface ICwJgZhcbService {

    CwJgZhcbListResult query(Date queryDate);

    void submit(CwJgZhcbSumbitParam param);

    List<CwKrbRow> sumByMonth(Date queryDate);

    CwJgZhcbListResult autoFill(Date queryDate);
}
