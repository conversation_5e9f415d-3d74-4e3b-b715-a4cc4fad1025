package org.jeecg.modules.cw.jw.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CwJwRow {
    private String name;
    private String unit;
    private BigDecimal pjzh;
    private BigDecimal pjdj;
    private BigDecimal yys;
    private BigDecimal ylj;
    private String type;
    private String remark;
    
    // 新增字段
    private BigDecimal yyc; // 月预测
    private BigDecimal drs; // 当日数
    private BigDecimal pjdh; // 平均单耗
    private BigDecimal jcb; // 节超比
    private BigDecimal dwcb; // 单位成本
}
