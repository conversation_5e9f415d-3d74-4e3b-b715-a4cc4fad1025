package org.jeecg.modules.cw.ckc.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.modules.cw.ckc.entity.CwCkcRow;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 采矿场综合成本表提交参数
 */
@Data
public class CwCkcZhcbSumbitParam {
    /**
     * 提交日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;

    /**
     * 基础数据
     */
    private BaseData base;

    /**
     * 行数据
     */
    private List<CwCkcRow> rows;
    
    /**
     * 是否为上半月预测
     * true: 上半月预测(1-15号)
     * false: 下半月预测(16-月末)
     */
    private Boolean isFirstHalf;
    
    /**
     * 基础数据类
     */
    @Data
    public static class BaseData {
        /**
         * 采剥总量
         */
        private BigDecimal cbzl;
        
        /**
         * 处理量预测
         */
        private BigDecimal cllyc;
    }
}
