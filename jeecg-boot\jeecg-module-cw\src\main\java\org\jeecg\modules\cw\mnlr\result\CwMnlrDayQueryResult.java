package org.jeecg.modules.cw.mnlr.result;

import lombok.Data;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrDayRow;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrMonthRow;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CwMnlrDayQueryResult {
    private Date queryDate;
    private List<CwMnlrDayRow> rows;
    private String qtfy;
    private String frdw;
    private String jh;
    private BigDecimal zcb;
}
