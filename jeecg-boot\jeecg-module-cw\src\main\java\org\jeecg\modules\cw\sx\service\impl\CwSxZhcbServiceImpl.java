package org.jeecg.modules.cw.sx.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.cw.base.constants.CwCllDataName;
import org.jeecg.modules.cw.base.entity.CwCllycData;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwCllDataService;
import org.jeecg.modules.cw.base.service.ICwCllycDataService;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.krb.entity.CwKrbRow;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrStatisticsDayService;
import org.jeecg.modules.cw.sx.entity.CwSxDay;
import org.jeecg.modules.cw.sx.entity.CwSxMonth;
import org.jeecg.modules.cw.sx.entity.CwSxRow;
import org.jeecg.modules.cw.sx.param.CwSxZhcbSumbitParam;
import org.jeecg.modules.cw.sx.result.CwSxZhcbListResult;
import org.jeecg.modules.cw.sx.service.ICwSxDayService;
import org.jeecg.modules.cw.sx.service.ICwSxMonthService;
import org.jeecg.modules.cw.sx.service.ICwSxZhcbService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 泗选厂综合成本业务实现类。
 * <p>
 * 主要职责：
 * <ul>
 *     <li>提供泗选厂综合成本相关的查询、保存、汇总等业务能力</li>
 *     <li>维护日表、月表及其与处理量、预算等基础数据之间的关系</li>
 *     <li>负责按需刷新缓存并重新计算 DRs（日成本）等关键指标</li>
 * </ul>
 * </p>
 */
@Service
@Slf4j
public class CwSxZhcbServiceImpl implements ICwSxZhcbService {

    private static final String DICT_TYPE = "sx";
    private static final String CACHE_KEY = "cw:sx:zhcb:";
    private static final String QUERY_CACHE_KEY = CACHE_KEY + "query";
    private static final String SUM_DRS_CACHE_KEY = CACHE_KEY + "sumDrs";
    private static final String SUM_BY_MONTH_CACHE_KEY = CACHE_KEY + "sumByMonth";
    private static final String NAME_SX = "sx";

    @Resource
    private ICwNameDictService nameDictService;
    @Resource
    private ICwSxDayService sxDayService;
    @Resource
    private ICwSxMonthService sxMonthService;
    @Resource
    private ICwCllDataService cwCllDataService;
    @Resource
    private ICwCllycDataService cwCllycDataService;
    @Lazy
    @Resource
    private ICwMnlrStatisticsDayService mnlrStatisticsDayService;

    /** 异步月重算单线程池 */
    private final java.util.concurrent.ExecutorService drsRecalcExecutor = java.util.concurrent.Executors.newSingleThreadExecutor(r -> {
        Thread t = new Thread(r, "sx-drs-recalc");
        t.setDaemon(true);
        return t;
    });

    @javax.annotation.PreDestroy
    public void shutdownExecutor() {
        drsRecalcExecutor.shutdown();
    }


    /**
     * 查询指定日期的泗选厂综合成本填报结果。
     *
     * <p>
     * 流程说明：
     * <ol>
     *     <li>判断查询日期处于上半月还是下半月</li>
     *     <li>查询字典、日数据（CwSxDay）、月数据（CwSxMonth）</li>
     *     <li>组装 {@link CwSxRow} 列表，计算月累计（ylj）与当日数（drs）</li>
     *     <li>补充处理量（sxCll）及处理量预测（cllyc）等附加信息</li>
     * </ol>
     * </p>
     *
     * @param queryDate 需要查询的日期
     * @return 查询结果封装 {@link CwSxZhcbListResult}
     */
    @Override
    public CwSxZhcbListResult query(Date queryDate) {
        // 1. 初始化结果对象
        CwSxZhcbListResult result = new CwSxZhcbListResult();
        result.setQueryDate(queryDate);

        // 2. 获取基础数据
        // 2.1 日期相关
        Date beginOfMonth = DateUtil.beginOfMonth(queryDate);
        Date endOfDay = DateUtil.endOfDay(queryDate);
        Date dayBeforeQuery = DateUtil.offsetDay(queryDate, -1);
        int currentDay = DateUtil.dayOfMonth(queryDate);
        boolean isFirstHalf = currentDay <= 15;
        result.setIsFirstHalf(isFirstHalf);

        // 2.2 项目字典
        List<CwNameDict> dict = nameDictService.queryList(DICT_TYPE);

        // 2.2.1 获取当月处理量预测(cllyc) 供缺失日数据自动补全
        BigDecimal monthCllyc = null;
        {
            Date monthBegin = DateUtil.beginOfMonth(queryDate);
            CwCllycData cllycData = cwCllycDataService.lambdaQuery()
                    .eq(CwCllycData::getRecordTime, monthBegin)
                    .eq(CwCllycData::getName, NAME_SX)
                    .one();
            if (ObjectUtil.isNotEmpty(cllycData)) {
                monthCllyc = cllycData.getCllyc();
            }
        }

        // 2.3 日表和月表数据
        List<CwSxDay> allDaysInMonth = sxDayService.lambdaQuery()
                .between(CwSxDay::getRecordTime, beginOfMonth, endOfDay)
                .list();
        List<CwSxMonth> months = sxMonthService.lambdaQuery()
                .ge(CwSxMonth::getRecordTime, beginOfMonth)
                .le(CwSxMonth::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();

        // === 自动补全缺失日数据 ===
        if (ObjectUtil.isNotEmpty(monthCllyc) && monthCllyc.compareTo(BigDecimal.ZERO) > 0) {
            List<CwSxDay> fill = autoFillMissingDays(beginOfMonth, queryDate, dict, monthCllyc, months);
            if (ObjectUtil.isNotEmpty(fill)) {
                allDaysInMonth.addAll(fill);
            }
        }

        // 3. 构建结果行数据
        Map<String, List<CwSxDay>> daysByName = allDaysInMonth.stream().collect(Collectors.groupingBy(CwSxDay::getName));

        List<CwSxRow> resRows = new ArrayList<>();

        for (CwNameDict d : dict) {
            CwSxRow row = new CwSxRow();
            BeanUtil.copyProperties(d, row);

            months.stream()
                    .filter(m -> d.getName().equals(m.getName()))
                    .findFirst()
                    .ifPresent(m -> fillMonthData(row, m, isFirstHalf));

            List<CwSxDay> itemDays = daysByName.get(d.getName());
            if (ObjectUtil.isNotEmpty(itemDays)) {
                itemDays.stream()
                        .filter(day -> DateUtil.isSameDay(queryDate, day.getRecordTime()))
                        .findFirst()
                        .ifPresent(day -> {
                            fillDayData(row, day);
                            if (day.getDrs() != null) {
                                row.setDrs(day.getDrs());
                            }
                        });

                BigDecimal ylj = itemDays.stream()
                        .filter(day -> !day.getRecordTime().after(dayBeforeQuery))
                        .map(CwSxDay::getDrs)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                row.setYlj(ylj);
            }

            resRows.add(row);
        }

        result.setRows(resRows);

        // 4. 设置额外数据
        // 4.1 处理量
        Map<String, String> sxCllDataMap = cwCllDataService.getRangeCllData(CwCllDataName.SZCLL, queryDate, queryDate);
        if(ObjectUtil.isNotEmpty(sxCllDataMap)){
            String todayKey = DateUtil.format(queryDate, "yyyy-MM-dd");
            String sxCllStr = sxCllDataMap.get(todayKey);
            if(ObjectUtil.isNotEmpty(sxCllStr)){
                result.setSxCll(new BigDecimal(sxCllStr));
            }
        }

        // 4.2 处理量预测
        CwCllycData cllycData = cwCllycDataService.lambdaQuery()
                .eq(CwCllycData::getRecordTime, beginOfMonth)
                .eq(CwCllycData::getName, NAME_SX)
                .one();
        if (ObjectUtil.isNotEmpty(cllycData) && ObjectUtil.isNotEmpty(cllycData.getCllyc())) {
            result.setCllyc(cllycData.getCllyc());
        }

        return result;
    }

    /**
     * 提交泗选厂综合成本填报数据。
     *
     * <p>
     * 主要步骤：
     * <ul>
     *     <li>保存/更新处理量及处理量预测</li>
     *     <li>清理目标日期的旧日数据并写入新数据</li>
     *     <li>保存或更新月数据（按上/下半月区分预测值）</li>
     *     <li>提交后立即重新计算当日 DRs 并清除相关缓存</li>
     * </ul>
     * </p>
     *
     * @param param 前端提交的综合成本参数
     */
    @Override
    public void submit(CwSxZhcbSumbitParam param) {
        Date submitDate = param.getSubmitDate();
        // 标识 cllyc 是否发生变化
        boolean cllycChanged = false;
        Date monthBegin = DateUtil.beginOfMonth(submitDate);
        
        // 更新处理量数据
        if (param.getBase() != null && param.getBase().getSxCll() != null) {
            BigDecimal sxCllValue = param.getBase().getSxCll();
            cwCllDataService.setCllData(CwCllDataName.SZCLL, sxCllValue.stripTrailingZeros().toPlainString(), submitDate);
        }

        // ========== 处理量预测 (cllyc) 的增、改、删 ==========
        BigDecimal newCllyc = param.getBase() != null ? param.getBase().getCllyc() : null;
        CwCllycData cllycData = cwCllycDataService.lambdaQuery()
                .eq(CwCllycData::getRecordTime, monthBegin)
                .eq(CwCllycData::getName, NAME_SX)
                .one();

        if (newCllyc != null) {
            if (cllycData == null) {
                cllycData = new CwCllycData();
                cllycData.setRecordTime(monthBegin);
                cllycData.setName(NAME_SX);
                cllycChanged = true;
            } else if (cllycData.getCllyc() == null || cllycData.getCllyc().compareTo(newCllyc) != 0) {
                cllycChanged = true;
            }

            cllycData.setCllyc(newCllyc);
            if (cllycData.getId() == null) {
                cwCllycDataService.save(cllycData);
            } else {
                cwCllycDataService.updateById(cllycData);
            }
        } else {
            if (cllycData != null) {
                cwCllycDataService.removeById(cllycData.getId());
                cllycChanged = true;
            }
        }

        // 更新日数据
        List<CwSxRow> rows = param.getRows();
        sxDayService.remove(new LambdaQueryWrapper<>(CwSxDay.class).eq(CwSxDay::getRecordTime, submitDate));
        
        // 获取已有的月数据
        List<CwSxMonth> existingMonths = sxMonthService.lambdaQuery()
                .ge(CwSxMonth::getRecordTime, DateUtil.beginOfMonth(submitDate))
                .le(CwSxMonth::getRecordTime, DateUtil.endOfMonth(submitDate))
                .list();
        Map<String, CwSxMonth> existingMonthMap = new HashMap<>();
        for (CwSxMonth month : existingMonths) {
            existingMonthMap.put(month.getName(), month);
        }
        
        ArrayList<CwSxDay> days = new ArrayList<>();
        // 转换并保存日数据
        for (CwSxRow row : rows) {
            CwSxDay day = new CwSxDay();
            BeanUtil.copyProperties(row, day);
            day.setRecordTime(submitDate);
            day.setDrs(BigDecimal.ZERO);
            days.add(day);
        }
        sxDayService.saveBatch(days);
        
        ArrayList<CwSxMonth> months = new ArrayList<>();
        // 组装月数据（包含预测值、预算等）
        Boolean isFirstHalf = param.getIsFirstHalf();
        
        for (CwSxRow row : rows) {
            CwSxMonth month;
            if (existingMonthMap.containsKey(row.getName())) {
                month = existingMonthMap.get(row.getName());
                
                if (ObjectUtil.isNotEmpty(row.getYyc())) {
                    BigDecimal forecastValue = row.getYyc();
                    if (isFirstHalf != null) {
                        if (isFirstHalf) {
                            month.setFirstHalfForecast(forecastValue);
                        } else {
                            month.setSecondHalfForecast(forecastValue);
                        }
                    }
                }
                
                if (ObjectUtil.isNotEmpty(row.getPjdj())) {
                    month.setPjdj(row.getPjdj());
                }
                if (ObjectUtil.isNotEmpty(row.getYys())) {
                    month.setYys(row.getYys());
                }

            } else {
                month = new CwSxMonth();
                BeanUtil.copyProperties(row, month);
                month.setRecordTime(DateUtil.beginOfMonth(submitDate));
            }
            months.add(month);
        }
        sxMonthService.saveOrUpdateBatch(months);

        // 根据 cllyc 是否变更决定重算范围
        if (cllycChanged) {
            Date endOfMonth = DateUtil.endOfMonth(submitDate);
            drsRecalcExecutor.submit(() -> recalculateDrs(monthBegin, endOfMonth));
        } else {
            this.recalculateDrsByDate(submitDate);
        }
    }

    /**
     * 计算指定日期当日 DRs（成本）总和。
     *
     * @param queryDate 目标日期
     * @return DRs 合计
     */
    @Override
    public BigDecimal sumDrs(Date queryDate) {
        return sxDayService.lambdaQuery()
                .eq(CwSxDay::getRecordTime, queryDate)
                .list()
                .stream()
                .map(CwSxDay::getDrs)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 汇总指定日期所在月份的各类型成本数据。
     *
     * @param queryDate 目标日期
     * @return {@link CwKrbRow} 列表，用于科软报表展示
     */
    @Override
    public List<CwKrbRow> sumByMonth(Date queryDate) {
        // 1. 初始化结果
        Map<String, CwKrbRow> result = CwKrbRow.takeDefaultKrbRows("泗选厂");

        // 2. 获取基础数据
        // 2.1 日期相关
        Date beginOfMonth = DateUtil.beginOfMonth(queryDate);
        Date endOfDay = DateUtil.endOfDay(queryDate);

        // 2.2 数据准备: 获取包含drs的日数据
        List<CwSxDay> allDays = sxDayService.lambdaQuery()
                .between(CwSxDay::getRecordTime, beginOfMonth, endOfDay)
                .list();
        List<CwSxMonth> months = sxMonthService.lambdaQuery()
                .ge(CwSxMonth::getRecordTime, beginOfMonth)
                .le(CwSxMonth::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();

        // 3. 定义计算分类行
        CwKrbRow clRow = result.get(CwKrbRow.CL);
        CwKrbRow bjRow = result.get(CwKrbRow.BJ);
        CwKrbRow rlRow = result.get(CwKrbRow.RL);
        CwKrbRow sRow = result.get(CwKrbRow.S);
        CwKrbRow dRow = result.get(CwKrbRow.D);
        CwKrbRow zzfyRow = result.get(CwKrbRow.ZZFY);

        // 4. 处理每天的数据
        for (CwSxDay day : allDays) {
            BigDecimal value = day.getDrs();
            if (ObjectUtil.isNotEmpty(value)) {
                if (DateUtil.isSameDay(queryDate, day.getRecordTime())) {
                    addToDailyTotal(day.getType(), value, clRow, bjRow, rlRow, sRow, dRow, zzfyRow);
                }
                addToMonthlyTotal(day.getType(), value, clRow, bjRow, rlRow, sRow, dRow, zzfyRow);
            }
        }

        // 5. 处理月预算数据
        for (CwSxMonth month : months) {
            BigDecimal yys = month.getYys();
            if (ObjectUtil.isNotEmpty(yys)) {
                addToBudget(month.getType(), yys, clRow, bjRow, rlRow, sRow, dRow, zzfyRow);
            }
        }

        return new ArrayList<>(result.values());
    }

    @Override
    public BigDecimal sumBudgetMonth(java.util.Date monthDate) {
        BigDecimal total = java.math.BigDecimal.ZERO;
        List<CwKrbRow> rows = this.sumByMonth(monthDate);
        if (rows != null) {
            for (CwKrbRow row : rows) {
                if (row != null && row.getYys() != null) {
                    total = total.add(row.getYys());
                }
            }
        }
        return total;
    }

    private void addToDailyTotal(String type, BigDecimal value,
                                 CwKrbRow clRow, CwKrbRow bjRow, CwKrbRow rlRow,
                                 CwKrbRow sRow, CwKrbRow dRow, CwKrbRow zzfyRow) {
        if (ObjectUtil.isEmpty(value)) {
            return;
        }
        switch (type) {
            case CwKrbRow.CL:
                clRow.setDrs(clRow.getDrs().add(value));
                break;
            case CwKrbRow.BJ:
                bjRow.setDrs(bjRow.getDrs().add(value));
                break;
            case CwKrbRow.RL:
                rlRow.setDrs(rlRow.getDrs().add(value));
                break;
            case CwKrbRow.S:
                sRow.setDrs(sRow.getDrs().add(value));
                break;
            case CwKrbRow.D:
                dRow.setDrs(dRow.getDrs().add(value));
                break;
            case CwKrbRow.ZZFY:
                zzfyRow.setDrs(zzfyRow.getDrs().add(value));
                break;
            default:
                break;
        }
    }

    private void addToMonthlyTotal(String type, BigDecimal value,
                                   CwKrbRow clRow, CwKrbRow bjRow, CwKrbRow rlRow,
                                   CwKrbRow sRow, CwKrbRow dRow, CwKrbRow zzfyRow) {
        if (ObjectUtil.isEmpty(value)) {
            return;
        }
        switch (type) {
            case CwKrbRow.CL:
                clRow.setRlj(clRow.getRlj().add(value));
                break;
            case CwKrbRow.BJ:
                bjRow.setRlj(bjRow.getRlj().add(value));
                break;
            case CwKrbRow.RL:
                rlRow.setRlj(rlRow.getRlj().add(value));
                break;
            case CwKrbRow.S:
                sRow.setRlj(sRow.getRlj().add(value));
                break;
            case CwKrbRow.D:
                dRow.setRlj(dRow.getRlj().add(value));
                break;
            case CwKrbRow.ZZFY:
                zzfyRow.setRlj(zzfyRow.getRlj().add(value));
                break;
            default:
                break;
        }
    }

    private void addToBudget(String type, BigDecimal value,
                             CwKrbRow clRow, CwKrbRow bjRow, CwKrbRow rlRow,
                             CwKrbRow sRow, CwKrbRow dRow, CwKrbRow zzfyRow) {
        if (ObjectUtil.isEmpty(value)) {
            return;
        }
        switch (type) {
            case CwKrbRow.CL:
                clRow.setYys(clRow.getYys().add(value));
                break;
            case CwKrbRow.BJ:
                bjRow.setYys(bjRow.getYys().add(value));
                break;
            case CwKrbRow.RL:
                rlRow.setYys(rlRow.getYys().add(value));
                break;
            case CwKrbRow.S:
                sRow.setYys(sRow.getYys().add(value));
                break;
            case CwKrbRow.D:
                dRow.setYys(dRow.getYys().add(value));
                break;
            case CwKrbRow.ZZFY:
                zzfyRow.setYys(zzfyRow.getYys().add(value));
                break;
            default:
                break;
        }
    }

    /**
     * 自动补全指定日期的数据并返回最新查询结果。
     *
     * @param queryDate 需要自动补全的日期
     * @return 最新查询结果
     */
    @Override
    public CwSxZhcbListResult autoFill(Date queryDate) {
        this.recalculateDrsByDate(queryDate);
        return query(queryDate);
    }

    /**
     * 重新计算指定日期的所有 DRs。
     *
     * @param date 目标日期
     */
    @Override
    public void recalculateDrsByDate(Date date) {
        log.info("开始重新计算泗选厂{}的drs", DateUtil.formatDate(date));
        recalculateDrs(date, date);
        log.info("结束重新计算泗选厂{}的drs", DateUtil.formatDate(date));
    }

    /**
     * 批量重新计算泗选厂历史所有 DRs。
     */
    @Override
    public void recalculateAllDrs() {
        log.info("开始重新计算泗选厂所有的drs");
        List<Date> dates = sxDayService.list().stream()
                .map(CwSxDay::getRecordTime)
                .map(DateUtil::beginOfDay)
                .distinct()
                .collect(Collectors.toList());

        for (Date date : dates) {
            try {
                recalculateDrsByDate(date);
            } catch (Exception e) {
                log.error("重新计算泗选厂drs失败，日期: {}", DateUtil.formatDate(date), e);
            }
        }
        log.info("结束重新计算泗选厂所有的drs");
    }

    /**
     * 重新计算今日 DRs。
     */
    @Override
    public void recalculateTodayDrs() {
        recalculateDrsByDate(new Date());
    }

    private void recalculateDrs(Date startDate, Date endDate) {
        List<CwSxDay> allDaysInRange = sxDayService.lambdaQuery()
                .between(CwSxDay::getRecordTime, DateUtil.beginOfDay(startDate), DateUtil.endOfDay(endDate))
                .list();

        if (allDaysInRange.isEmpty()) {
            return;
        }

        Map<String, List<CwSxDay>> daysByMonth = allDaysInRange.stream()
                .collect(Collectors.groupingBy(d -> DateUtil.format(d.getRecordTime(), "yyyy-MM")));

        List<CwSxDay> daysToUpdate = new ArrayList<>();

        for (Map.Entry<String, List<CwSxDay>> entry : daysByMonth.entrySet()) {
            String monthKey = entry.getKey();
            List<CwSxDay> daysInMonth = entry.getValue();
            Date monthDate = DateUtil.parse(monthKey, "yyyy-MM");
            Date beginOfMonth = DateUtil.beginOfMonth(monthDate);
            Date endOfMonth = DateUtil.endOfMonth(monthDate);

            List<CwSxMonth> months = sxMonthService.lambdaQuery()
                    .ge(CwSxMonth::getRecordTime, beginOfMonth)
                    .le(CwSxMonth::getRecordTime, endOfMonth)
                    .list();

            Map<String, BigDecimal> cllycDataMap = getMonthCllycData(monthDate);
            BigDecimal cllyc = cllycDataMap.get(monthKey);

            Map<String, BigDecimal> sxCllDataMap = getRangeSxCllData(beginOfMonth, endOfMonth);

            for (CwSxDay day : daysInMonth) {
                String dateKey = DateUtil.format(day.getRecordTime(), "yyyy-MM-dd");
                BigDecimal sxCll = sxCllDataMap.get(dateKey);

                BigDecimal drs = calculateDrs(day, cllyc, sxCll, months);

                day.setDrs(drs);
                daysToUpdate.add(day);
            }
        }

        if (!daysToUpdate.isEmpty()) {
            sxDayService.updateBatchById(daysToUpdate);
        }

        // 同步重算模拟利润（日）
        mnlrStatisticsDayService.recalcRange(startDate, endDate);
    }

    private BigDecimal calculateDrs(CwSxDay day, BigDecimal cllyc, BigDecimal sxCll, List<CwSxMonth> months) {
            String name = day.getName();
            Date recordTime = day.getRecordTime();
            BigDecimal yyc = null;
            BigDecimal pjdj = null;

            // 检查是否为月末最后一天
            boolean isLastDayOfMonth = DateUtil.isSameDay(recordTime, DateUtil.endOfMonth(recordTime));

            for (CwSxMonth month : months) {
                if (name.equals(month.getName())) {
                    pjdj = month.getPjdj();

                    // 最高优先级：月末预测（仅影响月末最后一天）
                    if (isLastDayOfMonth && month.getMonthEndForecast() != null) {
                        yyc = month.getMonthEndForecast();
                    }
                    // 第二优先级：下半月预测数据（影响整个月）
                    else if (month.getSecondHalfForecast() != null) {
                        yyc = month.getSecondHalfForecast();
                    }
                    // 第三优先级：根据日期使用月预算或下半月预测
                    else {
                        int recordDay = DateUtil.dayOfMonth(recordTime);
                        boolean recordIsFirstHalf = recordDay <= 15;

                        if (recordIsFirstHalf) {
                            // 上半月使用月预算
                            if (month.getYys() != null) {
                                yyc = month.getYys();
                            }
                        } else {
                            // 下半月使用下半月预测（但此时应该为null，因为上面已经检查过）
                            if (month.getSecondHalfForecast() != null) {
                                yyc = month.getSecondHalfForecast();
                            }
                        }
                    }
                    break;
                }
            }
    
            // 只有当月预算/预测、处理量预测和处理量都有值时，才计算当日数
            if (ObjectUtil.isNotEmpty(cllyc) && ObjectUtil.isNotEmpty(yyc) &&
                    cllyc.compareTo(BigDecimal.ZERO) > 0 && ObjectUtil.isNotEmpty(sxCll)) {
                BigDecimal dwcb = yyc.divide(cllyc, 10, BigDecimal.ROUND_HALF_UP);
                return dwcb.multiply(sxCll);
            }
    
            BigDecimal pjzh = day.getPjzh();
            if (ObjectUtil.isNotEmpty(pjzh) && ObjectUtil.isNotEmpty(pjdj)) {
                return pjzh.multiply(pjdj);
            }
    
            return BigDecimal.ZERO;
        }

    private Map<String, BigDecimal> getMonthCllycData(Date monthDate) {
        Map<String, BigDecimal> result = new HashMap<>();
        Date beginOfMonth = DateUtil.beginOfMonth(monthDate);
        String monthKey = DateUtil.format(beginOfMonth, "yyyy-MM");

        CwCllycData cllycData = cwCllycDataService.lambdaQuery()
                .eq(CwCllycData::getRecordTime, beginOfMonth)
                .eq(CwCllycData::getName, NAME_SX)
                .one();

        if (ObjectUtil.isNotEmpty(cllycData) && ObjectUtil.isNotEmpty(cllycData.getCllyc())) {
            result.put(monthKey, cllycData.getCllyc());
        }

        return result;
    }

    private Map<String, BigDecimal> getRangeSxCllData(Date startDate, Date endDate) {
        Map<String, String> dataMap = cwCllDataService.getRangeCllData(CwCllDataName.SZCLL, startDate, endDate);
        Map<String, BigDecimal> result = new HashMap<>();
        if (ObjectUtil.isNotEmpty(dataMap)) {
            dataMap.forEach((dateStr, valStr) -> {
                if (ObjectUtil.isNotEmpty(valStr)) {
                    result.put(dateStr, new BigDecimal(valStr));
                }
            });
        }
        return result;
    }

    private void fillMonthData(CwSxRow row, CwSxMonth month, boolean isFirstHalf) {
        row.setPjdj(month.getPjdj());
        row.setYys(month.getYys());

        if (!isFirstHalf && month.getSecondHalfForecast() != null) {
            row.setYyc(month.getSecondHalfForecast());
        }
    }

    private void fillDayData(CwSxRow row, CwSxDay day) {
        row.setPjzh(day.getPjzh());
        row.setRemark(day.getRemark());
    }

    private List<CwSxDay> autoFillMissingDays(Date beginOfMonth, Date queryDate,
                                             List<CwNameDict> dict,
                                             BigDecimal monthCllyc,
                                             List<CwSxMonth> months) {
        Date endOfDay = DateUtil.endOfDay(queryDate);
        List<CwSxDay> existing = sxDayService.lambdaQuery()
                .between(CwSxDay::getRecordTime, beginOfMonth, endOfDay)
                .list();
        Set<String> existKeys = existing.stream()
                .map(d -> d.getName() + "#" + DateUtil.format(d.getRecordTime(), "yyyy-MM-dd"))
                .collect(Collectors.toSet());

        Map<String, BigDecimal> sxCllMap = getRangeSxCllData(beginOfMonth, endOfDay);

        List<CwSxDay> needInsert = new ArrayList<>();
        Date iter = beginOfMonth;
        while (!iter.after(queryDate)) {
            String dateKey = DateUtil.format(iter, "yyyy-MM-dd");
            BigDecimal sxCll = sxCllMap.get(dateKey);
            if (ObjectUtil.isEmpty(sxCll)) {
                iter = DateUtil.offsetDay(iter, 1);
                continue;
            }
            for (CwNameDict d : dict) {
                String k = d.getName() + "#" + dateKey;
                if (existKeys.contains(k)) continue;
                CwSxDay newDay = new CwSxDay();
                BeanUtil.copyProperties(d, newDay);
                newDay.resetForCopy();
                newDay.setRecordTime(iter);
                BigDecimal drsVal = calculateDrs(newDay, monthCllyc, sxCll, months);
                newDay.setDrs(drsVal);
                needInsert.add(newDay);
            }
            iter = DateUtil.offsetDay(iter, 1);
        }
        if (!needInsert.isEmpty()) {
            sxDayService.saveBatch(needInsert);
        }
        return needInsert;
    }
}