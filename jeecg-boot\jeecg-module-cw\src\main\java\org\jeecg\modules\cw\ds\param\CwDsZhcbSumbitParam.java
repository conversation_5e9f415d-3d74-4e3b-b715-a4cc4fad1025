package org.jeecg.modules.cw.ds.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.modules.cw.base.entity.CwDwBase;
import org.jeecg.modules.cw.ckc.entity.CwCkcRow;
import org.jeecg.modules.cw.ds.entity.CwDsRow;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 大山厂综合成本表提交参数
 */
@Data
public class CwDsZhcbSumbitParam {
    /**
     * 提交日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;

    /**
     * 基础数据
     */
    private BaseData base;

    /**
     * 行数据
     */
    private List<CwDsRow> rows;
    
    /**
     * 是否为上半月预测
     * true: 上半月预测(1-15号)
     * false: 下半月预测(16-月末)
     */
    private Boolean isFirstHalf;
    
    /**
     * 基础数据类
     */
    @Data
    public static class BaseData {
        /**
         * 处理量
         */
        private BigDecimal dsCll;
        
        /**
         * 处理量预测
         */
        private BigDecimal cllyc;
    }
}
