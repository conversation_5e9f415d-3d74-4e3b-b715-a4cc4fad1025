package org.jeecg.modules.cw.jscb.result;

import lombok.Data;
import org.jeecg.modules.cw.jlfx.entity.CwJlfxRow;
import org.jeecg.modules.cw.jscb.entity.CwJscbRow;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CwJscbQueryResult {
    private Date queryDate;
    private List<CwJscbRow> rows;
    private BigDecimal dyf;
    private BigDecimal zycb;
    private BigDecimal msft;
    private BigDecimal clcb;
    private BigDecimal zcb;
}
