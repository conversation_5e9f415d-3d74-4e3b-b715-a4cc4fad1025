package org.jeecg.modules.cw.qtfy.service;

import org.jeecg.modules.cw.qtfy.entity.CwQtfy;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.cw.qtfy.param.CwQtfySumbitParam;
import org.jeecg.modules.cw.qtfy.result.CwQtfyListResult;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 其他费用
 * @Author: jeecg-boot
 * @Date:   2025-01-02
 * @Version: V1.0
 */
public interface ICwQtfyService extends IService<CwQtfy> {

    CwQtfyListResult query(Date queryDate);

    void submit(CwQtfySumbitParam param);

    BigDecimal sumMonth(Date queryDate);

    BigDecimal sumDay(Date queryDate);

    CwQtfyListResult autoFill(Date queryDate);

    /**
     * 查询指定日期范围内的其他费用总和
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 其他费用总和
     */
    BigDecimal sumPeriod(Date startDate, Date endDate);
}
