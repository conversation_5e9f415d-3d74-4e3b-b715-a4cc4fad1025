package org.jeecg.modules.cw.qtfy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.cw.base.entity.CwDwBase;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwDwBaseService;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.ckc.entity.CwCkcDay;
import org.jeecg.modules.cw.ckc.entity.CwCkcMonth;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrMonth;
import org.jeecg.modules.cw.qtfy.entity.CwQtfy;
import org.jeecg.modules.cw.qtfy.entity.CwQtfy;
import org.jeecg.modules.cw.qtfy.entity.CwQtfyRow;
import org.jeecg.modules.cw.qtfy.mapper.CwQtfyMapper;
import org.jeecg.modules.cw.qtfy.param.CwQtfySumbitParam;
import org.jeecg.modules.cw.qtfy.result.CwQtfyListResult;
import org.jeecg.modules.cw.qtfy.service.ICwQtfyService;
import org.jeecg.modules.cw.qtfy.service.ICwQtfyService;
import org.jeecg.modules.cw.qtfy.entity.CwQtfy;
import org.jeecg.modules.cw.qtfy.mapper.CwQtfyMapper;
import org.jeecg.modules.cw.qtfy.service.ICwQtfyService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 其他费用
 * @Author: jeecg-boot
 * @Date:   2025-01-02
 * @Version: V1.0
 */
@Service
public class CwQtfyServiceImpl extends ServiceImpl<CwQtfyMapper, CwQtfy> implements ICwQtfyService {

    @Resource
    private ICwNameDictService nameDictService;


    @Override
    public CwQtfyListResult query(Date queryDate) {
        CwQtfyListResult result = new CwQtfyListResult();
        result.setQueryDate(queryDate);
        // 项目字典
        List<CwNameDict> dict = nameDictService.queryList("qtfy");
        // 当日数据列表
        List<CwQtfy> days = this.lambdaQuery()
                .between(CwQtfy::getRecordTime, DateUtil.beginOfDay(queryDate), DateUtil.endOfDay(queryDate))
                .list();

        // 月累计（不含当日）数据列表
        List<CwQtfy> monthDays = this.lambdaQuery()
                .ge(CwQtfy::getRecordTime, DateUtil.beginOfMonth(queryDate))
                .lt(CwQtfy::getRecordTime, DateUtil.beginOfDay(queryDate))
                .list();

        // 合并数据
        List<CwQtfyRow> resRows = new ArrayList<>();
        for (CwNameDict d : dict) {
            CwQtfyRow row = new CwQtfyRow();
            BeanUtil.copyProperties(d, row);

            // 当日金额
            days.stream()
                    .filter(d1 -> d.getName().equals(d1.getName()))
                    .findFirst()
                    .ifPresent(d1 -> row.setJe(d1.getJe()));

            // 月累计金额（不含当日）
            BigDecimal ylj = monthDays.stream()
                    .filter(m1 -> d.getName().equals(m1.getName()) && ObjectUtil.isNotEmpty(m1.getJe()))
                    .map(CwQtfy::getJe)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            row.setYlj(ylj);

            resRows.add(row);
        }
        result.setRows(resRows);
        // 结果
        return result;
    }

    @Override
    public void submit(CwQtfySumbitParam param) {
        Date submitDate = param.getSubmitDate();
        // 更新行数据
        List<CwQtfyRow> rows = param.getRows();
        this.remove(new LambdaQueryWrapper<>(CwQtfy.class)
                .eq(CwQtfy::getRecordTime, submitDate));
        ArrayList<CwQtfy> months = new ArrayList<>();
        for (CwQtfyRow row : rows) {
            CwQtfy month = new CwQtfy();
            BeanUtil.copyProperties(row, month);
            month.setRecordTime(submitDate);
            months.add(month);
        }
        this.saveBatch(months);
    }

    @Override
    public BigDecimal sumMonth(Date queryDate) {
        BigDecimal sum = new BigDecimal(0);
        // 准备数据
        List<CwQtfy> allDays = this.lambdaQuery()
                .ge(CwQtfy::getRecordTime, DateUtil.beginOfMonth(queryDate))
                .le(CwQtfy::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();
        // 计算
        for (CwQtfy d : allDays) {
            if (ObjectUtil.isNotEmpty(d.getJe())) {
                sum = sum.add(d.getJe());
            }
        }
        return sum;
    }

    @Override
    public BigDecimal sumDay(Date queryDate) {
        BigDecimal sum = new BigDecimal(0);
        // 准备数据
        List<CwQtfy> allDays = this.lambdaQuery()
                .between(CwQtfy::getRecordTime, DateUtil.beginOfDay(queryDate), DateUtil.endOfDay(queryDate))
                .list();
        // 计算
        for (CwQtfy d : allDays) {
            if (ObjectUtil.isNotEmpty(d.getJe())) {
                sum = sum.add(d.getJe());
            }
        }
        return sum;
    }

    @Override
    public CwQtfyListResult autoFill(Date queryDate) {
        // 从昨天开始，到本月1号为止，倒序查找可复制的数据源
        Date firstDayOfMonth = DateUtil.beginOfMonth(queryDate);
        for (Date dateToTry = DateUtil.offsetDay(queryDate, -1); !dateToTry.before(firstDayOfMonth); dateToTry = DateUtil.offsetDay(dateToTry, -1)) {
            long count = this.lambdaQuery()
                    .between(CwQtfy::getRecordTime, DateUtil.beginOfDay(dateToTry), DateUtil.endOfDay(dateToTry))
                    .count();
            if (count > 0) {
                // 找到数据，立即使用这个日期进行查询并返回
                return query(dateToTry);
            }
        }
        // 如果本月（从昨天到1号）没有任何数据，则默认查询当天
        return query(queryDate);
    }

    @Override
    public BigDecimal sumPeriod(Date startDate, Date endDate) {
        // 查询指定日期范围内的其他费用总和
        // 这里实现一个简单的方法，通过逐日查询并累加
        BigDecimal sum = BigDecimal.ZERO;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        
        while (!calendar.getTime().after(endDate)) {
            BigDecimal daySum = sumDay(calendar.getTime());
            if (daySum != null) {
                sum = sum.add(daySum);
            }
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        
        return sum;
    }
}
