package org.jeecg.modules.cw.ckc.service.impl;

import org.jeecg.modules.cw.ckc.entity.CwCkcDay;
import org.jeecg.modules.cw.ckc.mapper.CwCkcDayMapper;
import org.jeecg.modules.cw.ckc.service.ICwCkcDayService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description: 采矿场-日填报
 * @Author: jeecg-boot
 * @Date:   2024-12-06
 * @Version: V1.0
 */
@Service
public class CwCkcDayServiceImpl extends ServiceImpl<CwCkcDayMapper, CwCkcDay> implements ICwCkcDayService {

    @Resource
    private CwCkcDayMapper ckcDayMapper;
}
