package org.jeecg.modules.cw.mnlr.service;

import org.jeecg.modules.cw.mnlr.entity.CwMnlrStatisticsDay;
import com.baomidou.mybatisplus.extension.service.IService;
import java.math.BigDecimal;

/**
 * @Description: 矿模拟利润统计数据（日）
 * @Author: jeecg-boot
 * @Date:   2025-02-20
 * @Version: V1.0
 */
public interface ICwMnlrStatisticsDayService extends IService<CwMnlrStatisticsDay> {

    void addOrEdit(CwMnlrStatisticsDay cwMnlrStatisticsDay);

    /**
     * 获取指定日期的模拟利润
     * @param date 日期
     * @return mnlr 值，若不存在返回 BigDecimal.ZERO
     */
    BigDecimal getDayProfit(java.util.Date date);

    /**
     * 统计区间（含边界）内的模拟利润之和
     * @param start 开始日期（含）
     * @param end   结束日期（含）
     * @return 区间利润合计
     */
    BigDecimal sumMnlrRange(java.util.Date start, java.util.Date end);

    /**
     * 统计区间（含边界）内的销售收入之和
     * @param start 开始日期（含）
     * @param end   结束日期（含）
     * @return 区间销售收入合计
     */
    BigDecimal sumSlRange(java.util.Date start, java.util.Date end);

    /**
     * 重算指定日期的模拟利润（日级别）
     * @param date 日期
     */
    void recalcDay(java.util.Date date);

    /**
     * 重算指定年月(yyyy, 1-12)范围内的所有模拟利润（日级别）
     * @param year  年份
     * @param month 月份（1-12）
     */
    void recalcMonth(int year, int month);

    /**
     * 全量重算：根据现有 CwMnlrDay 数据，重新计算并回写全部日统计
     */
    void recalcAll();

    /**
     * 按日期范围（含）批量重算日模拟利润。
     * @param start 开始日期（含），为 null 则忽略
     * @param end   结束日期（含），为 null 则忽略
     */
    void recalcRange(java.util.Date start, java.util.Date end);
}
