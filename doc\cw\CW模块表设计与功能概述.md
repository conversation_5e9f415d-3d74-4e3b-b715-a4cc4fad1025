# 财务模块（cw）表设计与功能概述

> 说明：本文档基于 `jeecg-boot/jeecg-module-cw` 目录下现有代码整理，旨在为二次开发或新成员快速了解财务模块（Comprehensive cost，简称 **cw**）的整体设计与主要功能提供参考。

---

## 1. 模块定位与目标
1. 对接各业务单位（采矿场、大山厂、泗选、精尾、新技术、检化、机关等）的 **成本、费用、能耗、产量** 等数据；
2. 通过统一的数据结构（日数据表 `*_day`、月/年统计表等）沉淀底层数据，为 **月度综合成本**、**年度预算**、**经营分析** 等报表提供支撑；
3. 提供**自动数据填充**、**成本重算**、**历史数据迁移** 等后台任务，降低手工维护量；
4. 暴露 Restful API 供前端及第三方系统使用，同时支持 Excel 导入/导出。

---

## 2. 目录结构概览
```text
jeecg-module-cw/
├─ base/          # 通用基础数据、工具、公共 Service
├─ entity/        # 公共实体（仅 BaseDayEntity，目前所有日表继承自它）
├─ quartz/        # Quartz 定时任务
├─ statistics/    # 统计分析相关（如月报表）
│
├─ ckc/           # 采矿场子模块
├─ ds/            # 大山厂子模块
├─ sx/            # 泗选子模块
├─ jw/            # 精尾子模块
├─ xjs/           # 新技术子模块
├─ jh/            # 检化子模块
├─ jg/            # 机关子模块
├─ qtfy/          # 其他费用子模块
├─ frdw/          # 法人单位子模块
├─ jlfx/          # 检粒分析子模块
├─ cbwc/ / cbgc/  # 采剥外包、采剥工程 ……（其余子模块同理）
└─ migration/     # 历史数据迁移脚本
```
> 约定：每个业务子模块内部通常包含 `controller / service / service/impl / mapper / entity / param / result` 等包，遵循 **SpringBoot + MyBatis-Plus** 的三层架构。

---

## 3. 通用表设计
### 3.1 `BaseDayEntity`
所有"日数据"表均继承该抽象类，公共字段如下：
| 字段 | 说明 | 类型 |
| ---- | ---- | ---- |
| id | 主键(UUID，MyBatis-Plus `ASSIGN_ID`) | String |
| create_by / create_time | 创建人/时间 | String / Date |
| update_by / update_time | 更新人/时间 | String / Date |
| record_time | 业务日期（**必填**，仅日期部分有效） | Date |

> 通过 `resetForCopy()` 可在**自动填充**场景下快速复制并置空主键及创建信息。

### 3.2 典型日表 (`cw_*_day`)
- 命名：`cw_<模块简称>_day` ；
- 继承 `BaseDayEntity` 并追加各业务字段；
- 常见字段示例：
  - `sys_org_code`：数据归属部门（租户、多组织场景）
  - `name / type / unit`：项目名称 / 分类 / 计量单位
  - `drs`：当日数（核心业务值，可由后台重算）
  - `pjzh / pjdj`：平均总耗 / 平均单价 …
  - `remark`：备注

### 3.3 行对象(`*_Row`)
位于各子模块 `entity` 包，**无数据库映射**，仅用于：
1. Controller 之间的数据传输；
2. Excel 导入/导出、表单批量提交。

### 3.4 其他公共表
| 表名 | 作用 | 典型字段 |
| ---- | ---- | ---- |
| `cw_base_data` | **基础参数表**：存放矿长成本、单价系数等年度/月度/日度参数 | `name,data,type,unit,record_time` |

> 通过 `ICwKBaseService` 可按天/月/年维度读写该表，提供灵活的费用/系数配置能力。

---

## 4. 业务子模块一览
| 子模块 | 目录 | 主要日表 | 说明 |
| ------ | ---- | -------- | ---- |
| 采矿场 | `ckc` | `cw_ckc_day` / `cw_ckc_zhcb` | 采矿场能耗、材料、人工、设备折旧等成本数据 |
| 大山厂 | `ds` | `cw_ds_day` / `cw_ds_zhcb` | 粗/精矿产量及能耗、药剂费用等 |
| 泗选   | `sx` | `cw_sx_day` / `cw_sx_zhcb` | 选矿厂指标数据 |
| 精尾   | `jw` | `cw_jw_day` / `cw_jw_zhcb` | 精尾矿处理成本 |
| 新技术 | `xjs`| `cw_xjs_day` / `cw_xjs_zhcb`| 新技术中心试验消耗 |
| 检化   | `jh` | `cw_jh_day`               | 检化中心化验费用 |
| 机关   | `jg` | `cw_jg_day`               | 机关后勤及行政费用 |
| 其他费用| `qtfy`| `cw_qtfy`                | 各类无法归属到生产单位的费用 |
| 法人单位| `frdw`| `cw_frdw_day`            | 法人单位成本 |
| …… | | | 其余子模块以同样模式扩展 |

> **统一原则**：子模块如需"综合成本"功能，应实现 `ICw<模块>ZhcbService` 接口，并在 `quartz.AutoFillJob` 与 `DrsRecalculateJob` 中注册对应逻辑。

---

## 5. 后台定时任务（Quartz）
| 任务类 | 触发时间(建议) | 功能概要 |
| ------ | ------------- | -------- |
| `AutoFillJob` | 每日凌晨 | 根据配置规则 **自动复制** 上期数据，避免漏报；针对 xjs/jh/jg/qtfy 等有差异化策略 |
| `DrsRecalculateJob` | 每日 1:00 | 批量 **重算当日数(drs)**，调用各子模块 `recalculateDrsByDate`，保证实时性 |
| `DrsTodayRecalculateJob` | 每日 23:55 | 当日报表收口前再次重算 |
| `CopyTableJob` | 每月首日 0:30 | 按月自动复制生成 `_history` 表，进行冷数据归档 |
| `PullHistoryDataJob` | 按需手动 | 将历史系统数据迁移至新表结构 |
| `PullCllCbzlJob` / `PullClAndJgJob` | 定时 | 从外部接口同步 **材料领料、机关费用** 等数据 |
| `MnlrStatisticsJob` | 月末 | 地煤泥入浓度统计示例 |

---

## 6. 统计与分析（statistics 子模块）
- 提供 `CwStatisticsController` 系列接口，支持 **月 / 季 / 年** 维度聚合；
- 常见输出字段：**单吨成本、金额、环比、同比**；
- 可通过扩展 `mapper/xml` 自定义复杂 SQL 或使用 `MyBatis-Plus Wrapper` 动态拼装。

---

## 7. 开发注意事项
1. **字段枚举 / 字典**：尽量使用系统字典（`@Dict`）维护，便于前端下拉渲染；
2. **缓存**：部分耗时统计已使用 `Redis` 进行缓存（Key 形如 `cw:<module>:zhcb:`），修改逻辑后需考虑缓存清理；
3. **事务管理**：服务实现均声明在 `service.impl`，默认已启用 Spring 事务，如有跨库或批量操作请显式 `@Transactional`；
4. **自动代码生成**：新子模块可先运行 Jeecg-Boot 代码生成器，再对照现有实现补齐 `zhcb` 相关业务与 Quartz 注册；
5. **单元测试**：建议以子模块维度编写 `SpringBootTest`，并利用 **MockMvc** 对 Controller 层进行覆盖。

---

## 8. 后续扩展建议
- **指标口径管理**：抽象指标定义表，支持动态计算公式；
- **多维度成本归集**：对接 BI 工具（如 ECharts + Apache Superset）实现钻取分析；
- **权限细粒度**：基于部门、角色、数据维度（record_time）进行行级权限控制；
- **CI/CD**：在 `CopyTableJob` 之后自动导出冷数据至离线数仓或对象存储。

---

## 9. 综合成本服务（`*ZhcbServiceImpl`）方法一览
各业务子模块（ckc、ds、sx、jw、xjs、jh、jg …）的综合成本服务实现类均遵循统一接口 `ICw<模块>ZhcbService`，故方法签名与职责高度一致，具体说明如下：

| 方法 | 作用 | 被调用场景 |
| ---- | ---- | -------- |
| `query(Date queryDate)` | 查询指定日期的综合成本填报数据，组装行对象、计算当日数和月累计，并返回 `xxxZhcbListResult`。 | 前端页面加载/切换日期 |
| `submit(xxxZhcbSumbitParam param)` | 保存/更新当日填报：写入处理量&预测值、替换日表数据、更新月表预测、重算当天 `drs` 并清理缓存。 | 前端"保存/提交"按钮 |
| `autoFill(Date queryDate)` | 根据模块规则自动复制历史数据，生成或补全当日填报记录。 | Quartz `AutoFillJob`；前端"自动填充" |
| `sumDrs(Date queryDate)` | 统计指定日期所有行的 `drs` 合计（BigDecimal）。 | 经营日报、矿日表汇总 |
| `sumByMonth(Date queryDate)` | 计算整月累计并返回材料/备件/人工等分类行列表 `CwKrbRow`。 | 月度分析、对标报表 |
| `recalculateDrsByDate(Date date)` | 重算指定日期的 `drs`。 | `DrsRecalculateJob`（每日凌晨） |
| `recalculateAllDrs()` | 遍历历史数据并重算，常用于模型调整、大批量导入后手动触发。 | 运维手工、工具脚本 |
| `recalculateTodayDrs()` | 23:55 再次重算当日 `drs`，确保日报准确性。 | `DrsTodayRecalculateJob` |

此外，各实现类内部还包含：
- `calculateDrs(...)`：核心算法，根据行指标 × 处理量/预测 × 系数生成当日数；
- `fillDayData / fillMonthData`：实体 ↔ 行对象转换 & 预算填充；
- `getMonthCllycData / getRangeCllData`：读取基础参数表的缓存化工具；
- `addToDailyTotal / addToMonthlyTotal / addToBudget`：按分类汇总行数据；
- `recalculateDrs(startDate, endDate)`：区间重算通用封装。

> 缓存键统一以 `cw:<module>:zhcb:` 为前缀，包括 `query` / `sumDrs` / `sumByMonth` 三类。重算或提交后需删除对应缓存，代码已在各实现中处理。 