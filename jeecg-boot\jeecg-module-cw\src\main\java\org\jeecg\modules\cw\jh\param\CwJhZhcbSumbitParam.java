package org.jeecg.modules.cw.jh.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.modules.cw.base.entity.CwDwBase;
import org.jeecg.modules.cw.jh.entity.CwJhRow;
import org.jeecg.modules.cw.jw.entity.CwJwRow;

import java.util.Date;
import java.util.List;

@Data
public class CwJhZhcbSumbitParam {
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;
    private List<CwJhRow> rows;
}
