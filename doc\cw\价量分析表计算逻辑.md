# 价量分析表计算逻辑

本文档记录 **价量分析表（JLFx）** 中各产品“影响利润金额”计算方法，便于业务人员与开发人员查阅。

> 说明：所有金额单位为 **万元**，`1.13` 为增值税系数（17% 税率），`× 系数` 为产品毛利率或折算系数。

---

## 1. 产品售价对利润的影响

| 产品 | 系数 | 是否去税 | 计算公式 |
|------|------|----------|----------|
| 含铜 | 0.90 | 是 | `(实际售价 ÷ 1.13 – 计划售价 ÷ 1.13) × 实际销量 ÷ 10000 × 0.90` |
| 含金 | 0.84 | 否 | `(实际售价 – 计划售价) × 实际销量 ÷ 10000 × 0.84` |
| 含银 | 0.75 | 是 | `(实际售价 ÷ 1.13 – 计划售价 ÷ 1.13) × 实际销量 ÷ 10000 × 0.75` |
| 硫精矿 | 1.00 | 是 | `(实际售价 ÷ 1.13 – 计划售价 ÷ 1.13) × 实际销量 ÷ 10000` |
| 钼产品 | 1.00 | 是 | `(实际售价 ÷ 1.13 – 计划售价 ÷ 1.13) × 实际销量 ÷ 10000` |

公式要点：
1. **去税**：含金以外的产品需先除以 `1.13` 去除增值税，再计算价格差。
2. **价格差**：`实际售价 - 计划售价`（或去税后售价差）。
3. **销量**：乘当月 **实际销量**。
4. **单位换算**：`÷ 10000` 将元转换为万元。
5. **系数**：按产品乘以毛利率/折算系数。

---

## 2. 产品销量对利润的影响

| 产品 | 系数 | 是否去税 | 计算公式 |
|------|------|----------|----------|
| 含铜 | 0.90 | 是 | `(实际销量 – 计划销量) × 计划售价 ÷ 1.13 ÷ 10000 × 0.90` |
| 含金 | 0.84 | 否 | `(实际销量 – 计划销量) × 计划售价 ÷ 10000 × 0.84` |
| 含银 | 0.75 | 是 | `(实际销量 – 计划销量) × 计划售价 ÷ 1.13 ÷ 10000 × 0.75` |
| 硫精矿 | 1.00 | 是 | `(实际销量 – 计划销量) × 计划售价 ÷ 1.13 ÷ 10000` |
| 钼产品 | 1.00 | 是 | `(实际销量 – 计划销量) × 计划售价 ÷ 1.13 ÷ 10000` |

公式要点：
1. **销量差**：`实际销量 - 计划销量`。
2. **基准单价**：使用 **计划售价**（同样根据是否去税）
3. **后续与售价影响相同**：先去税→乘销量差→单位换算→乘系数。

---

## 3. 总影响利润金额

```math
总影响 = \sum(售价影响) + \sum(销量影响)
```

在前端实现中，对应变量：
- `sjJeList`：售价影响列表
- `xlJeList`：销量影响列表
- `jeSum = sjSum + xlSum` 为最终合计。

---

> 更新时间：{{DATE}} 