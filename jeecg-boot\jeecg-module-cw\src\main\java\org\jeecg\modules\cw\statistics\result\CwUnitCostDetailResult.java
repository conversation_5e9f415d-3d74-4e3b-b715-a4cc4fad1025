package org.jeecg.modules.cw.statistics.result;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 单位成本详细页面结果
 */
@Data
public class CwUnitCostDetailResult {
    private String unit;        // 单位名称
    private String dimension;   // month | year
    private String period;      // yyyy-MM 或 yyyy

    private BigDecimal actualTotalCost;
    private BigDecimal planTotalCost;
    private BigDecimal actualTonCost;
    private BigDecimal planTonCost;

    /** 与去年同期相比 = 本期实际 - 去年同期实际 */
    private BigDecimal yoy;

    /** 成本构成明细 */
    private List<CwCostBreakdownResult> breakdown;
} 