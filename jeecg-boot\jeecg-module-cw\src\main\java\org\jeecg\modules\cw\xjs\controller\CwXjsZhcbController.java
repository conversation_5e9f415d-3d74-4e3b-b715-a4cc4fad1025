package org.jeecg.modules.cw.xjs.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.cw.xjs.param.CwXjsZhcbSumbitParam;
import org.jeecg.modules.cw.xjs.result.CwXjsZhcbListResult;
import org.jeecg.modules.cw.xjs.service.ICwXjsZhcbService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Description: 新技术-综合成本表
 * @Author: tang
 * @Date: 2024-12-06
 * @Version: V1.0
 */
@Api(tags = "新技术-综合财报")
@RestController
@RequestMapping("/xjs/zhcb")
@Slf4j
public class CwXjsZhcbController {

    @Resource
    private ICwXjsZhcbService xjsZhcbService;


    @ApiOperation(value = "新技术-综合成本-列表查询", notes = "新技术-综合成本-列表查询")
    @GetMapping(value = "/query")
    public Result<CwXjsZhcbListResult> query(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        CwXjsZhcbListResult result = xjsZhcbService.query(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "新技术-综合成本-自动填充", notes = "新技术-综合成本-自动填充")
    @GetMapping(value = "/autoFill")
    public Result<CwXjsZhcbListResult> autoFill(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        CwXjsZhcbListResult result = xjsZhcbService.autoFill(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "新技术-综合成本-提交", notes = "新技术-综合成本-提交")
    @PostMapping(value = "/submit")
    public Result<String> submit(@RequestBody CwXjsZhcbSumbitParam submitParam) {
        xjsZhcbService.submit(submitParam);
        return Result.OK("提交成功");
    }
}
