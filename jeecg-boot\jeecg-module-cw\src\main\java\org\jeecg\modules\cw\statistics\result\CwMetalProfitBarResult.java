package org.jeecg.modules.cw.statistics.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 金属利润柱状图数据
 * <AUTHOR>
 */
@Data
@ApiModel("金属利润柱状图结果")
public class CwMetalProfitBarResult {

    /** 金属名称，如 铜/金/银/硫精矿/钼产品 */
    @ApiModelProperty("金属名称")
    private String metal;

    /** 计划利润（万元） */
    @ApiModelProperty("计划利润，单位：万元（已废弃）")
    private BigDecimal plan;

    /** 实际利润（万元） */
    @ApiModelProperty("实际利润，单位：万元（已废弃）")
    private BigDecimal actual;

    /** 售价差异影响利润（万元） */
    @ApiModelProperty("售价差异影响利润，单位：万元")
    private BigDecimal priceImpact;

    /** 销量差异影响利润（万元） */
    @ApiModelProperty("销量差异影响利润，单位：万元")
    private BigDecimal volumeImpact;
} 