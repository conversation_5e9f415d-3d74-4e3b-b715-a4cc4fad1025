package org.jeecg.modules.cw.xjs.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.cw.xjs.entity.CwXjsLrcs;
import org.jeecg.modules.cw.xjs.param.CwXjsLrcsSumbitParam;
import org.jeecg.modules.cw.xjs.param.CwXjsZhcbSumbitParam;
import org.jeecg.modules.cw.xjs.result.CwXjsLrcsListResult;
import org.jeecg.modules.cw.xjs.result.CwXjsZhcbListResult;
import org.jeecg.modules.cw.xjs.service.ICwXjsLrcsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 新技术利润测算
 * @Author: jeecg-boot
 * @Date:   2025-04-08
 * @Version: V1.0
 */
@Api(tags="新技术利润测算")
@RestController
@RequestMapping("/xjs/cwXjsLrcs")
@Slf4j
public class CwXjsLrcsController extends JeecgController<CwXjsLrcs, ICwXjsLrcsService> {
	@Autowired
	private ICwXjsLrcsService cwXjsLrcsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwXjsLrcs
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "新技术利润测算-分页列表查询")
	@ApiOperation(value="新技术利润测算-分页列表查询", notes="新技术利润测算-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CwXjsLrcs>> queryPageList(CwXjsLrcs cwXjsLrcs,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwXjsLrcs> queryWrapper = QueryGenerator.initQueryWrapper(cwXjsLrcs, req.getParameterMap());
		Page<CwXjsLrcs> page = new Page<CwXjsLrcs>(pageNo, pageSize);
		IPage<CwXjsLrcs> pageList = cwXjsLrcsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwXjsLrcs
	 * @return
	 */
	@AutoLog(value = "新技术利润测算-添加")
	@ApiOperation(value="新技术利润测算-添加", notes="新技术利润测算-添加")
	@RequiresPermissions("xjs:cw_xjs_lrcs:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwXjsLrcs cwXjsLrcs) {
		cwXjsLrcsService.save(cwXjsLrcs);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwXjsLrcs
	 * @return
	 */
	@AutoLog(value = "新技术利润测算-编辑")
	@ApiOperation(value="新技术利润测算-编辑", notes="新技术利润测算-编辑")
	@RequiresPermissions("xjs:cw_xjs_lrcs:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwXjsLrcs cwXjsLrcs) {
		cwXjsLrcsService.updateById(cwXjsLrcs);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "新技术利润测算-通过id删除")
	@ApiOperation(value="新技术利润测算-通过id删除", notes="新技术利润测算-通过id删除")
	@RequiresPermissions("xjs:cw_xjs_lrcs:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwXjsLrcsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "新技术利润测算-批量删除")
	@ApiOperation(value="新技术利润测算-批量删除", notes="新技术利润测算-批量删除")
	@RequiresPermissions("xjs:cw_xjs_lrcs:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwXjsLrcsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "新技术利润测算-通过id查询")
	@ApiOperation(value="新技术利润测算-通过id查询", notes="新技术利润测算-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwXjsLrcs> queryById(@RequestParam(name="id",required=true) String id) {
		CwXjsLrcs cwXjsLrcs = cwXjsLrcsService.getById(id);
		if(cwXjsLrcs==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwXjsLrcs);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwXjsLrcs
    */
    @RequiresPermissions("xjs:cw_xjs_lrcs:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwXjsLrcs cwXjsLrcs) {
        return super.exportXls(request, cwXjsLrcs, CwXjsLrcs.class, "新技术利润测算");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("xjs:cw_xjs_lrcs:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwXjsLrcs.class);
    }

	 @ApiOperation(value = "新技术-利润测算-列表查询", notes = "新技术-利润测算-列表查询")
	 @GetMapping(value = "/query")
	 public Result<CwXjsLrcsListResult> query(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
		 CwXjsLrcsListResult result = cwXjsLrcsService.query(queryDate);
		 return Result.ok(result);
	 }

	 @ApiOperation(value = "新技术-利润测算-提交", notes = "新技术-利润测算-提交")
	 @PostMapping(value = "/submit")
	 public Result<String> submit(@RequestBody CwXjsLrcsSumbitParam submitParam) {
		 cwXjsLrcsService.submit(submitParam);
		 return Result.OK("提交成功");
	 }
}
