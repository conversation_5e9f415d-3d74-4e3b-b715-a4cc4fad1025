package org.jeecg.modules.cw.mnlr.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 矿模拟利润统计数据（日）
 * @Author: jeecg-boot
 * @Date:   2025-02-20
 * @Version: V1.0
 */
@Data
@TableName("cw_mnlr_statistics_day")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cw_mnlr_statistics_day对象", description="矿模拟利润统计数据（日）")
public class CwMnlrStatisticsDay implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**实际销售收入*/
	@Excel(name = "实际销售收入", width = 15)
    @ApiModelProperty(value = "实际销售收入")
    private java.lang.String sl;
	/**金属总成本*/
	@Excel(name = "金属总成本", width = 15)
    @ApiModelProperty(value = "金属总成本")
    private java.math.BigDecimal cb;
	/**分公司及多经单位利润*/
	@Excel(name = "分公司及多经单位利润", width = 15)
    @ApiModelProperty(value = "分公司及多经单位利润")
    private java.math.BigDecimal djlr;
	/**其他*/
	@Excel(name = "其他", width = 15)
    @ApiModelProperty(value = "其他")
    private java.math.BigDecimal qt;
	/**全矿模拟利润*/
	@Excel(name = "全矿模拟利润", width = 15)
    @ApiModelProperty(value = "全矿模拟利润")
    private java.math.BigDecimal mnlr;
	/**公司进度计划*/
	@Excel(name = "公司进度计划", width = 15)
    @ApiModelProperty(value = "公司进度计划")
    private java.math.BigDecimal gsjh;
	/**进度计划比*/
	@Excel(name = "进度计划比", width = 15)
    @ApiModelProperty(value = "进度计划比")
    private java.math.BigDecimal jhb;
	/**时间*/
	@Excel(name = "时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "时间")
    private java.util.Date recordTime;
	/**单位*/
	@Excel(name = "单位", width = 15)
    @ApiModelProperty(value = "单位")
    private java.lang.String unit;
}
