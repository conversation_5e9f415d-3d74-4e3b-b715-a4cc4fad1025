package org.jeecg.modules.cw.jw.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.cw.jw.entity.CwJwMonth;
import org.jeecg.modules.cw.jw.service.ICwJwMonthService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 精尾厂-月填报
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
@Api(tags="精尾厂-月填报")
@RestController
@RequestMapping("/jw/cwJwMonth")
@Slf4j
public class CwJwMonthController extends JeecgController<CwJwMonth, ICwJwMonthService> {
	@Autowired
	private ICwJwMonthService cwJwMonthService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwJwMonth
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "精尾厂-月填报-分页列表查询")
	@ApiOperation(value="精尾厂-月填报-分页列表查询", notes="精尾厂-月填报-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CwJwMonth>> queryPageList(CwJwMonth cwJwMonth,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwJwMonth> queryWrapper = QueryGenerator.initQueryWrapper(cwJwMonth, req.getParameterMap());
		Page<CwJwMonth> page = new Page<CwJwMonth>(pageNo, pageSize);
		IPage<CwJwMonth> pageList = cwJwMonthService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwJwMonth
	 * @return
	 */
	@AutoLog(value = "精尾厂-月填报-添加")
	@ApiOperation(value="精尾厂-月填报-添加", notes="精尾厂-月填报-添加")
	@RequiresPermissions("jw:cw_jw_month:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwJwMonth cwJwMonth) {
		cwJwMonthService.save(cwJwMonth);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwJwMonth
	 * @return
	 */
	@AutoLog(value = "精尾厂-月填报-编辑")
	@ApiOperation(value="精尾厂-月填报-编辑", notes="精尾厂-月填报-编辑")
	@RequiresPermissions("jw:cw_jw_month:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwJwMonth cwJwMonth) {
		cwJwMonthService.updateById(cwJwMonth);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "精尾厂-月填报-通过id删除")
	@ApiOperation(value="精尾厂-月填报-通过id删除", notes="精尾厂-月填报-通过id删除")
	@RequiresPermissions("jw:cw_jw_month:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwJwMonthService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "精尾厂-月填报-批量删除")
	@ApiOperation(value="精尾厂-月填报-批量删除", notes="精尾厂-月填报-批量删除")
	@RequiresPermissions("jw:cw_jw_month:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwJwMonthService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "精尾厂-月填报-通过id查询")
	@ApiOperation(value="精尾厂-月填报-通过id查询", notes="精尾厂-月填报-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwJwMonth> queryById(@RequestParam(name="id",required=true) String id) {
		CwJwMonth cwJwMonth = cwJwMonthService.getById(id);
		if(cwJwMonth==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwJwMonth);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwJwMonth
    */
    @RequiresPermissions("jw:cw_jw_month:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwJwMonth cwJwMonth) {
        return super.exportXls(request, cwJwMonth, CwJwMonth.class, "精尾厂-月填报");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("jw:cw_jw_month:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwJwMonth.class);
    }

}
