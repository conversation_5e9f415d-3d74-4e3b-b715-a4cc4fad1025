package org.jeecg.modules.cw.ds.service;

import org.jeecg.modules.cw.ds.param.CwDsZhcbSumbitParam;
import org.jeecg.modules.cw.ds.result.CwDsZhcbListResult;
import org.jeecg.modules.cw.krb.entity.CwKrbRow;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface ICwDsZhcbService {

    CwDsZhcbListResult query(Date queryDate);

    void submit(CwDsZhcbSumbitParam param);

    BigDecimal sumDrs(Date queryDate);

    List<CwKrbRow> sumByMonth(Date queryDate);

    /**
     * 统计指定月份的预算合计金额（yys 字段之和）
     */
    BigDecimal sumBudgetMonth(Date monthDate);

    CwDsZhcbListResult autoFill(Date queryDate);

    /**
     * 根据日期重新计算drs
     * @param date
     */
    void recalculateDrsByDate(Date date);

    /**
     * 重新计算所有的drs
     */
    void recalculateAllDrs();

    /**
     * 重新计算当日的drs
     */
    void recalculateTodayDrs();
}
