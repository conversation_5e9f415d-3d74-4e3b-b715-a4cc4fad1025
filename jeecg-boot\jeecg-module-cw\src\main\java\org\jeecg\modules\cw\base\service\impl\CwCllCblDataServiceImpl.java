package org.jeecg.modules.cw.base.service.impl;

import org.jeecg.modules.cw.base.entity.CwCllCblData;
import org.jeecg.modules.cw.base.mapper.CwCllCblDataMapper;
import org.jeecg.modules.cw.base.service.ICwCllCblDataService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 处理量采拨量数据
 * @Author: jeecg-boot
 * @Date:   2025-03-12
 * @Version: V1.0
 */
@Service
public class CwCllCblDataServiceImpl extends ServiceImpl<CwCllCblDataMapper, CwCllCblData> implements ICwCllCblDataService {

}
