<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.cw.base.mapper.CwNameDictMapper">


    <select id="queryListByDw" resultType="org.jeecg.modules.cw.base.entity.CwNameDict">
        SELECT cnd.id,
               cnd.NAME,
               cnd.dw,
               cnd.type,
               cnd.unit,
               ctd.NAME     AS type_name,
               cnd.name_key AS name_key
        FROM cw_name_dict cnd
                 LEFT JOIN cw_type_dict ctd ON cnd.type = ctd.type
        WHERE dw = #{dw}
        ORDER BY ctd.sort_code,
                 cnd.sort_code
    </select>
</mapper>