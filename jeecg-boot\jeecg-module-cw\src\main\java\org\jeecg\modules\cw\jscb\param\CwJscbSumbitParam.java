package org.jeecg.modules.cw.jscb.param;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.modules.cw.jlfx.entity.CwJlfxRow;
import org.jeecg.modules.cw.jscb.entity.CwJscbRow;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CwJscbSumbitParam {
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;
    private List<CwJscbRow> rows;
    private String dyf;
    private String zycb;
    private String msft;
    private String clcb;
}
