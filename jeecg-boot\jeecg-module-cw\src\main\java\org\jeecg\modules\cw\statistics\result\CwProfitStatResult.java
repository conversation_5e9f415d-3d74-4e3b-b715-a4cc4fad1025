package org.jeecg.modules.cw.statistics.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 利润统计结果（按天 / 月 / 年）
 * 说明：
 * 1. 年累计不返回环比字段（为 null）。
 * 2. 所有金额单位统一为"万元"。
 *
 * <AUTHOR>
 */
@Data
@ApiModel("利润统计结果")
public class CwProfitStatResult {

    /** 统计维度 day / month / year */
    @ApiModelProperty(value = "统计维度 day/month/year")
    private String dimension;

    /** 本期标识：yyyy-MM-dd | yyyy-MM | yyyy */
    @ApiModelProperty(value = "本期（格式：日yyyy-MM-dd / 月yyyy-MM / 年yyyy）")
    private String period;

    /** 本期利润 */
    @ApiModelProperty(value = "本期利润，单位：万元")
    private BigDecimal profit;

    /** 环比差值（本期-上期），年维度返回 null */
    @ApiModelProperty(value = "环比差值，单位：万元")
    private BigDecimal hb;

    /** 同比差值（本期-去年同期） */
    @ApiModelProperty(value = "同比差值，单位：万元")
    private BigDecimal tb;

    /** 计划比差值（本期-计划值） */
    @ApiModelProperty(value = "与计划比差值，单位：万元")
    private BigDecimal jhb;

    /** 统一单位 */
    @ApiModelProperty(value = "单位，默认：万元")
    private String unit = "万元";
} 