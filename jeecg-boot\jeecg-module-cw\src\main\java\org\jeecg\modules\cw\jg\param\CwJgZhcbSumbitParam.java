package org.jeecg.modules.cw.jg.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.modules.cw.jg.entity.CwJgRow;
import org.jeecg.modules.cw.jh.entity.CwJhRow;

import java.util.Date;
import java.util.List;

@Data
public class CwJgZhcbSumbitParam {
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;
    private List<CwJgRow> rows;
}
