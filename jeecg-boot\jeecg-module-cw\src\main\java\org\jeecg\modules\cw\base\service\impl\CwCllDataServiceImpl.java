package org.jeecg.modules.cw.base.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.cw.base.constants.CwCllDataName;
import org.jeecg.modules.cw.base.entity.CwCllData;
import org.jeecg.modules.cw.base.mapper.CwCllDataMapper;
import org.jeecg.modules.cw.base.service.ICwCllDataService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 处理量数据
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Service
public class CwCllDataServiceImpl extends ServiceImpl<CwCllDataMapper, CwCllData> implements ICwCllDataService {

    @Override
    public String getCllData(CwCllDataName type, Date recordTime) {
        return getCllData(type.getName(), recordTime);
    }

    @Override
    public String getCllData(String name, Date recordTime) {
        LambdaQueryWrapper<CwCllData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CwCllData::getName, name)
                .eq(CwCllData::getRecordTime, DateUtil.beginOfDay(recordTime));
        CwCllData cllData = getOne(queryWrapper);
        return ObjectUtil.isNotEmpty(cllData) ? cllData.getData() : null;
    }
    
    @Override
    public void setCllData(CwCllDataName type, String data, Date recordTime) {
        setCllData(type.getName(), data, recordTime);
    }

    @Override
    public void setCllData(String name, String data, Date recordTime) {
        // 先查询是否存在
        LambdaQueryWrapper<CwCllData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CwCllData::getName, name)
                .eq(CwCllData::getRecordTime, DateUtil.beginOfDay(recordTime));
        CwCllData cllData = getOne(queryWrapper);
        
        if (ObjectUtil.isEmpty(cllData)) {
            // 不存在则新增
            cllData = new CwCllData();
            cllData.setName(name);
            cllData.setData(data);
            cllData.setRecordTime(DateUtil.beginOfDay(recordTime));
            save(cllData);
        } else {
            // 存在则更新
            cllData.setData(data);
            updateById(cllData);
        }
    }
    
    @Override
    public Map<String, String> getMonthCllData(CwCllDataName type, Date monthDate) {
        return getMonthCllData(type.getName(), monthDate);
    }
    
    @Override
    public Map<String, String> getMonthCllData(String name, Date monthDate) {
        // 获取月初和月末
        Date beginOfMonth = DateUtil.beginOfMonth(monthDate);
        Date endOfMonth = DateUtil.endOfMonth(monthDate);
        
        // 调用范围查询方法
        return getRangeCllData(name, beginOfMonth, endOfMonth);
    }
    
    @Override
    public Map<String, String> getRangeCllData(CwCllDataName type, Date startDate, Date endDate) {
        return getRangeCllData(type.getName(), startDate, endDate);
    }
    
    @Override
    public Map<String, String> getRangeCllData(String name, Date startDate, Date endDate) {
        // 查询日期范围内的所有数据
        LambdaQueryWrapper<CwCllData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CwCllData::getName, name)
                .ge(CwCllData::getRecordTime, DateUtil.beginOfDay(startDate))
                .le(CwCllData::getRecordTime, DateUtil.endOfDay(endDate));
        
        List<CwCllData> dataList = list(queryWrapper);
        
        // 转换为Map
        Map<String, String> resultMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(dataList)) {
            resultMap = dataList.stream()
                    .collect(Collectors.toMap(
                            data -> DateUtil.format(data.getRecordTime(), "yyyy-MM-dd"),
                            CwCllData::getData
                    ));
        }
        
        return resultMap;
    }
}
