package org.jeecg.modules.cw.base.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 厂矿单位基础处理数据
 * @Author: jeecg-boot
 * @Date:   2024-12-10
 * @Version: V1.0
 */
@Data
@TableName("cw_dw_base")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cw_dw_base对象", description="厂矿单位基础处理数据")
public class CwDwBase implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	
	/**记录时间*/
	@Excel(name = "记录时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "记录时间")
    private Date recordTime;
	
	/**采剥总量*/
	@Excel(name = "采剥总量", width = 15)
    @ApiModelProperty(value = "采剥总量")
    private BigDecimal cbzl;
	
	/**总周转量*/
	@Excel(name = "总周转量", width = 15)
    @ApiModelProperty(value = "总周转量")
    private BigDecimal zzzl;
	
	/**总爆破量*/
	@Excel(name = "总爆破量", width = 15)
    @ApiModelProperty(value = "总爆破量")
    private BigDecimal zbpl;
    
    /**大山处理量*/
	@Excel(name = "大山处理量", width = 15)
    @ApiModelProperty(value = "大山处理量")
    private BigDecimal dsCll;
	
	/**泗选处理量*/
	@Excel(name = "泗选处理量", width = 15)
    @ApiModelProperty(value = "泗选处理量")
    private BigDecimal sxCll;
	
	/**精尾处理量*/
	@Excel(name = "精尾处理量", width = 15)
    @ApiModelProperty(value = "精尾处理量")
    private BigDecimal jwCll;
}
