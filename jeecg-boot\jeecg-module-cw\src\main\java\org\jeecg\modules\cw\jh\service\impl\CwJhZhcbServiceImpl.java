package org.jeecg.modules.cw.jh.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.ckc.entity.CwCkcDay;
import org.jeecg.modules.cw.jh.entity.CwJhDay;
import org.jeecg.modules.cw.jh.entity.CwJhMonth;
import org.jeecg.modules.cw.jh.entity.CwJhRow;
import org.jeecg.modules.cw.jh.param.CwJhZhcbSumbitParam;
import org.jeecg.modules.cw.jh.result.CwJhZhcbListResult;
import org.jeecg.modules.cw.jh.service.ICwJhDayService;
import org.jeecg.modules.cw.jh.service.ICwJhMonthService;
import org.jeecg.modules.cw.jh.service.ICwJhZhcbService;
import org.jeecg.modules.cw.krb.entity.CwKrbRow;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 检化中心-日填报
 * @Author: jeecg-boot
 * @Date: 2024-12-06
 * @Version: V1.0
 */
@Service
public class CwJhZhcbServiceImpl implements ICwJhZhcbService {

    private static final String DICT_TYPE = "jh";
    private static final String CACHE_KEY = "cw:jh:zhcb:";
    private static final String QUERY_CACHE_KEY = CACHE_KEY + "query";
    private static final String SUM_BY_MONTH_CACHE_KEY = CACHE_KEY + "sumByMonth";

    @Resource
    private ICwNameDictService nameDictService;
    @Resource
    private ICwJhDayService jhDayService;
    @Resource
    private ICwJhMonthService jhMonthService;


    @Override
    public CwJhZhcbListResult query(Date queryDate) {
        CwJhZhcbListResult result = new CwJhZhcbListResult();
        result.setQueryDate(queryDate);
        // 项目字典
        List<CwNameDict> dict = nameDictService.queryList(DICT_TYPE);
        // 已有列表
        List<CwJhDay> allDays = jhDayService.lambdaQuery()
                .between(CwJhDay::getRecordTime, DateUtil.beginOfMonth(queryDate), DateUtil.endOfDay(queryDate))
                .list();
        List<CwJhDay> days = allDays.stream()
                .filter(d -> DateUtil.isSameDay(queryDate, d.getRecordTime()))
                .collect(Collectors.toList());
        List<CwJhMonth> months = jhMonthService.lambdaQuery()
                .ge(CwJhMonth::getRecordTime, DateUtil.beginOfMonth(queryDate))
                .le(CwJhMonth::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();
        // 合并数据
        List<CwJhRow> resRows = new ArrayList<>();
        for (CwNameDict d : dict) {
            CwJhRow row = new CwJhRow();
            BeanUtil.copyProperties(d, row);
            // 数据
            months.stream().filter(d1 -> d.getName().equals(d1.getName()))
                    .findFirst()
                    .ifPresent(d1 -> {
                        row.setYys(d1.getYys());
                    });
            days.stream().filter(d1 -> d.getName().equals(d1.getName()))
                    .findFirst()
                    .ifPresent(d1 -> {
                        row.setDrs(d1.getDrs());
                        row.setRemark(d1.getRemark());
                    });
            // 月累计 注！不包括当天
            BigDecimal sum = allDays.stream()
                    .filter(v -> d.getName().equals(v.getName())
                            && ObjectUtil.isNotEmpty(v.getDrs())
                            && !DateUtil.isSameDay(v.getRecordTime(), queryDate))
                    .map(CwJhDay::getDrs)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            row.setYlj(sum);
            resRows.add(row);
        }
        result.setRows(resRows);
        // 结果
        return result;
    }

    @Override
    public void submit(CwJhZhcbSumbitParam param) {
        Date submitDate = param.getSubmitDate();
        // 更新行数据
        List<CwJhRow> rows = param.getRows();
        jhDayService.remove(new LambdaQueryWrapper<>(CwJhDay.class).eq(CwJhDay::getRecordTime, submitDate));
        jhMonthService.remove(new LambdaQueryWrapper<>(CwJhMonth.class)
                .ge(CwJhMonth::getRecordTime, DateUtil.beginOfMonth(submitDate))
                .le(CwJhMonth::getRecordTime, DateUtil.endOfMonth(submitDate)));
        ArrayList<CwJhDay> days = new ArrayList<>();
        for (CwJhRow row : rows) {
            CwJhDay day = new CwJhDay();
            BeanUtil.copyProperties(row, day);
            day.setRecordTime(submitDate);
            days.add(day);
        }
        jhDayService.saveBatch(days);
        ArrayList<CwJhMonth> months = new ArrayList<>();
        for (CwJhRow row : rows) {
            CwJhMonth month = new CwJhMonth();
            BeanUtil.copyProperties(row, month);
            month.setRecordTime(submitDate);
            months.add(month);
        }
        jhMonthService.saveBatch(months);
    }

    @Override
    public List<CwKrbRow> sumByMonth(Date queryDate) {
        Map<String, CwKrbRow> result = CwKrbRow.takeDefaultKrbRows("检化中心");
        // 准备数据
        List<CwJhDay> allDays = jhDayService.lambdaQuery()
                .between(CwJhDay::getRecordTime, DateUtil.beginOfMonth(queryDate), DateUtil.endOfDay(queryDate))
                .list();
        List<CwJhMonth> months = jhMonthService.lambdaQuery()
                .ge(CwJhMonth::getRecordTime, DateUtil.beginOfMonth(queryDate))
                .le(CwJhMonth::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();
        Map<String, CwJhMonth> pjdjMap = CollStreamUtil.toIdentityMap(months, CwJhMonth::getName);
        // 计算
        CwKrbRow clRow = result.get(CwKrbRow.CL);
        CwKrbRow bjRow = result.get(CwKrbRow.BJ);
        CwKrbRow rlRow = result.get(CwKrbRow.RL);
        CwKrbRow dRow = result.get(CwKrbRow.D);
        CwKrbRow zzfyRow = result.get(CwKrbRow.ZZFY);
        for (CwJhDay d : allDays) {
            BigDecimal drs = d.getDrs();
            if (ObjectUtil.isNotEmpty(drs)) {
                // 当日数
                if (DateUtil.isSameDay(queryDate, d.getRecordTime())) {
                    switch (d.getType()) {
                        case "cl":
                            clRow.setDrs(clRow.getDrs().add(drs));
                            break;
                        case "bj":
                            bjRow.setDrs(bjRow.getDrs().add(drs));
                            break;
                        case "rl":
                            rlRow.setDrs(rlRow.getDrs().add(drs));
                            break;
                        case "d":
                            dRow.setDrs(dRow.getDrs().add(drs));
                            break;
                        case "zzfy":
                            zzfyRow.setDrs(zzfyRow.getDrs().add(drs));
                            break;
                    }
                }
                // 月累计
                switch (d.getType()) {
                    case "cl":
                        clRow.setRlj(clRow.getRlj().add(drs));
                        break;
                    case "bj":
                        bjRow.setRlj(bjRow.getRlj().add(drs));
                        break;
                    case "rl":
                        rlRow.setRlj(rlRow.getRlj().add(drs));
                        break;
                    case "d":
                        dRow.setRlj(dRow.getRlj().add(drs));
                        break;
                    case "zzfy":
                        zzfyRow.setRlj(zzfyRow.getRlj().add(drs));
                        break;
                }
            }
        }

        for (CwJhMonth month : months) {
            BigDecimal yys = month.getYys();
            if (ObjectUtil.isNotEmpty(month) && ObjectUtil.isNotEmpty(yys)) {
                // 日预算
                switch (month.getType()) {
                    case "cl":
                        clRow.setYys(clRow.getYys().add(yys));
                        break;
                    case "bj":
                        bjRow.setYys(bjRow.getYys().add(yys));
                        break;
                    case "rl":
                        rlRow.setYys(rlRow.getYys().add(yys));
                        break;
                    case "d":
                        dRow.setYys(dRow.getYys().add(yys));
                        break;
                    case "zzfy":
                        zzfyRow.setYys(zzfyRow.getYys().add(yys));
                        break;
                }
            }
        }
        // 结果
        return new ArrayList<>(result.values());
    }

    @Override
    public CwJhZhcbListResult autoFill(Date queryDate) {
        // 1. 新的填充逻辑
        Date dateToCopyFrom = null;
        int dayOfMonth = DateUtil.dayOfMonth(queryDate);

        // 最后一天不填充
        if (DateUtil.isLastDayOfMonth(queryDate)) {
            // 当天是最后一天时，不进行特殊填充
        } else if (dayOfMonth >= 2 && dayOfMonth <= 14) {
            // 2-14号，用1号填充
            dateToCopyFrom = DateUtil.beginOfMonth(queryDate);
        } else if (dayOfMonth >= 16 && dayOfMonth <= 30) {
            // 16-30号，用15号填充
            dateToCopyFrom = DateUtil.date(queryDate).setField(DateField.DAY_OF_MONTH, 15).toJdkDate();
        }

        if (dateToCopyFrom != null) {
            long count = jhDayService.lambdaQuery()
                    .between(CwJhDay::getRecordTime, DateUtil.beginOfDay(dateToCopyFrom), DateUtil.endOfDay(dateToCopyFrom))
                    .count();
            if (count > 0) {
                // 找到数据，立即使用这个日期进行查询并返回
                return query(dateToCopyFrom);
            }
        }

        // 2. 如果新逻辑未命中或源数据不存在，则默认查询当天
        return query(queryDate);
    }
}
