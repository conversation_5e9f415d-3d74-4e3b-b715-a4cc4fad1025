package org.jeecg.modules.cw.quartz.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrDay;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrDayService;
import org.jeecg.modules.cw.quartz.entity.DtsmmParam;
import org.jeecg.modules.cw.quartz.service.IPriceDataService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 价格数据服务实现
 */
@Slf4j
@Service
public class PriceDataServiceImpl implements IPriceDataService {

    private static final String APP_KEY = "y8EV26LJtYMEln8Y";
    private static final String APP_SECRET = "AwwdZU5I6G0mAJqyXopp4EuaEYy6h1Gb";
    private static final String TOKEN_URL = "http://***********:8082/data-service/api/token/generate?appKey=" + APP_KEY + "&appSecret=" + APP_SECRET;
    private static final String PRICE_URL = "http://***********:8082/data-service/api/dtsmm";
    private static final String OUTPUT_URL = "http://*************:8102/GetXXZX/getProductOutput";
    
    // 一次请求最大条数
    private static final int MAX_PAGE_SIZE = 1000;

    @Resource
    private ICwNameDictService nameDictService;
    @Resource
    private ICwMnlrDayService mnlrDayService;

    @Override
    public void getPriceData(Date date) {
        String dateStr = DateUtil.format(date, "yyyy-MM-dd");
        log.info("获取{}的价格数据", dateStr);
        
        try {
            // 判断是否为周末，如果是周末，则使用上一个工作日(周五)的日期
            DateTime dateTime = DateUtil.parseDate(dateStr);
            int dayOfWeek = dateTime.dayOfWeek();
            Date queryDate = date;
            
            // dayOfWeek: 1表示周日，2表示周一，...，7表示周六
            if (dayOfWeek == 7) { // 周六
                queryDate = DateUtil.offsetDay(date, -1); // 使用周五的日期
                log.info("当天为周六，使用周五({})的价格数据", DateUtil.format(queryDate, "yyyy-MM-dd"));
            } else if (dayOfWeek == 1) { // 周日
                queryDate = DateUtil.offsetDay(date, -2); // 使用周五的日期
                log.info("当天为周日，使用周五({})的价格数据", DateUtil.format(queryDate, "yyyy-MM-dd"));
            }
            
            // 获取token
            String token = getToken();
            if (ObjectUtil.isEmpty(token)) {
                return;
            }
            
            // 获取价格数据，只传递日期参数
            DtsmmParam dtsmmParam = DtsmmParam.builder()
                    .reportDate(queryDate)
                    .build();
            
            // 使用JSON.toJSONString时确保DtsmmParam中的Date字段正确序列化
            String jsonParams = JSON.toJSONString(dtsmmParam);
            log.debug("请求参数: {}", jsonParams);
            
            String dtsmmStr = HttpUtil.createPost(PRICE_URL)
                    .header("Content-Type", "application/json")
                    .auth(token)
                    .body(jsonParams)
                    .execute()
                    .body();
            
            processPriceData(dtsmmStr, date);
        } catch (Exception e) {
            log.error("获取{}的价格数据异常", dateStr, e);
        }
    }

    /**
     * 处理价格数据
     */
    private void processPriceData(String dtsmmStr, Date date) {
        log.info("价格数据响应：{}", dtsmmStr);
        JSONObject dtsmmObj = JSONObject.parseObject(dtsmmStr);
        
        if (ObjectUtil.isNotEmpty(dtsmmObj) && dtsmmObj.getBoolean("success")) {
            JSONArray dataArray = dtsmmObj.getJSONArray("data");
            if (ObjectUtil.isNotEmpty(dataArray) && dataArray.size() > 0) {
                // 数据列表
                JSONArray data = dataArray.getJSONArray(0);
                
                if (data == null || data.isEmpty()) {
                    log.error("价格数据列表为空");
                    return;
                }
                
                // 价格变量
                String cu = "0";
                String au = "0";
                String ag = "0";
                
                log.info("处理价格数据，共{}条", data.size());
                
                // 处理所有
                List<CwMnlrDay> addList = new ArrayList<>();
                
                for (int i = 0; i < data.size(); i++) {
                    JSONObject item = data.getJSONObject(i);
                    String metalType = item.getString("metal_type");
                    String value = item.getString("value");
                    
                    log.info("处理金属：{}, 价格：{}", metalType, value);
                    
                    if ("铜".equals(metalType)) {
                        cu = value;
                    } else if ("金".equals(metalType)) {
                        au = value;
                    } else if ("银".equals(metalType)) {
                        ag = value;
                    }
                }
                
                // 查询该日期是否有数据
                List<CwMnlrDay> existList = mnlrDayService.lambdaQuery()
                        .between(CwMnlrDay::getRecordTime, DateUtil.beginOfDay(date), DateUtil.endOfDay(date))
                        .list();
                
                if (existList != null && !existList.isEmpty()) {
                    // 更新已有数据
                    for (CwMnlrDay d : existList) {
                        switch (d.getType()) {
                            case "t":
                                d.setJg(new BigDecimal(cu));
                                // 计算成本
                                if (d.getXl() != null) {
                                    calculateCost(d);
                                }
                                break;
                            case "j":
                                d.setJg(new BigDecimal(au).multiply(BigDecimal.valueOf(1000)));
                                // 计算成本
                                if (d.getXl() != null) {
                                    calculateCost(d);
                                }
                                break;
                            case "y":
                                d.setJg(new BigDecimal(ag));
                                // 计算成本
                                if (d.getXl() != null) {
                                    calculateCost(d);
                                }
                                break;
                            case "l":
                                // 硫的价格固定为50
                                d.setJg(new BigDecimal("50"));
                                // 计算成本
                                if (d.getXl() != null) {
                                    calculateCost(d);
                                }
                                break;
                            case "ljk":
                                // 硫精矿使用当月1号的价格
                                BigDecimal ljkPrice = getMonthFirstDayPrice("ljk", date);
                                if (ljkPrice != null) {
                                    d.setJg(ljkPrice);
                                    log.info("硫精矿使用当月1号价格: {}", ljkPrice);
                                } else {
                                    log.warn("未找到硫精矿当月1号价格，保持原价格");
                                }
                                // 计算成本
                                if (d.getXl() != null && d.getJg() != null) {
                                    calculateCost(d);
                                }
                                break;
                            case "mjk":
                                // 钼精矿使用当月1号的价格
                                BigDecimal mjkPrice = getMonthFirstDayPrice("mjk", date);
                                if (mjkPrice != null) {
                                    d.setJg(mjkPrice);
                                    log.info("钼精矿使用当月1号价格: {}", mjkPrice);
                                } else {
                                    log.warn("未找到钼精矿当月1号价格，保持原价格");
                                }
                                // 计算成本
                                if (d.getXl() != null && d.getJg() != null) {
                                    calculateCost(d);
                                }
                                break;
                            default:
                                if (d.getXl() != null && d.getJg() != null) {
                                    calculateCost(d);
                                }
                        }
                    }
                    
                    // 更新
                    log.info("更新价格数据：{}", existList);
                    mnlrDayService.updateBatchById(existList);
                } else {
                    // 新增数据
                    List<CwNameDict> mnlrDicts = nameDictService.queryList("mnlrDay");
                    
                    if (mnlrDicts == null || mnlrDicts.isEmpty()) {
                        log.error("未找到mnlrDay配置数据");
                        return;
                    }
                    
                    log.info("根据配置创建新数据，配置项数量：{}", mnlrDicts.size());
                    
                    for (CwNameDict m : mnlrDicts) {
                        CwMnlrDay d = new CwMnlrDay();
                        BeanUtils.copyProperties(m, d);
                        switch (m.getType()) {
                            case "t":
                                d.setJg(new BigDecimal(cu));
                                break;
                            case "j":
                                d.setJg(new BigDecimal(au).multiply(BigDecimal.valueOf(1000)));
                                break;
                            case "y":
                                d.setJg(new BigDecimal(ag));
                                break;
                            case "l":
                                // 硫的价格固定为50
                                d.setJg(new BigDecimal("50"));
                                break;
                            case "ljk":
                                // 硫精矿使用当月1号的价格
                                BigDecimal ljkPrice = getMonthFirstDayPrice("ljk", date);
                                if (ljkPrice != null) {
                                    d.setJg(ljkPrice);
                                    log.info("新增硫精矿数据，使用当月1号价格: {}", ljkPrice);
                                } else {
                                    // 如果找不到当月1号价格，可以设置一个默认值或者不设置价格
                                    log.warn("未找到硫精矿当月1号价格，不设置价格");
                                }
                                break;
                            case "mjk":
                                // 钼精矿使用当月1号的价格
                                BigDecimal mjkPrice = getMonthFirstDayPrice("mjk", date);
                                if (mjkPrice != null) {
                                    d.setJg(mjkPrice);
                                    log.info("新增钼精矿数据，使用当月1号价格: {}", mjkPrice);
                                } else {
                                    // 如果找不到当月1号价格，可以设置一个默认值或者不设置价格
                                    log.warn("未找到钼精矿当月1号价格，不设置价格");
                                }
                                break;
                        }
                        d.setRecordTime(date);
                        d.setId(null);
                        // 默认设置成本为0
                        d.setCb(BigDecimal.ZERO);
                        addList.add(d);
                    }
                    
                    // 新增
                    if (!addList.isEmpty()) {
                        log.info("新增价格数据：{}", addList);
                        mnlrDayService.saveBatch(addList);
                    }
                }
            } else {
                log.error("价格数据为空");
            }
        } else {
            log.error("获取价格数据失败：{}", dtsmmObj != null ? dtsmmObj.getString("errorMsg") : "响应为空");
        }
    }

    @Override
    public void getOutputDataAndUpdate(Date date) {
        String dateStr = DateUtil.format(date, "yyyy-MM-dd");
        log.info("获取{}的产量数据", dateStr);
        
        String clJson = HttpUtil.get(OUTPUT_URL + "?startDate=" + dateStr);
        log.info("产品产量：{}", clJson);
        JSONObject cjJsonObj = JSONObject.parseObject(clJson);
        
        if (ObjectUtil.isNotEmpty(cjJsonObj) && cjJsonObj.getBoolean("Code")) {
            JSONObject data = cjJsonObj.getJSONObject("Data");
            // 铜金属量
            String tjkjsl = data.getString("tjkjsl");
            // 硫折合量 - 使用铜的销量计算
//            BigDecimal copperOutput = new BigDecimal(tjkjsl);
//            BigDecimal sulfurOutput = copperOutput.multiply(new BigDecimal("0.3")).divide(new BigDecimal("0.2453"), 6, BigDecimal.ROUND_HALF_UP);
//            String ljkzhl = sulfurOutput.toString();
            String ljkzhl = data.getString("ljkzhl");
            String ljkjsl = data.getString("ljkjsl");
            // 钼精矿折合量
            String mjkzhl = data.getString("mjkzhl");
            // 金金属量
            String tjkhjl = data.getString("tjkhjl");
            // 银金属量
            String tjkhyl = data.getString("tjkhyl");

            log.info("铜销量: {}, 计算得到的硫销量: {}, 钼精矿销量: {}", tjkjsl, ljkzhl, mjkzhl);

            // 查询当天数据
            List<CwMnlrDay> mnlrDays = mnlrDayService.lambdaQuery()
                    .between(CwMnlrDay::getRecordTime, DateUtil.beginOfDay(date), DateUtil.endOfDay(date))
                    .list();

            // 合并字典配置数据，确保所有配置项都有数据记录
            if (mnlrDays == null) {
                mnlrDays = new ArrayList<>();
            }

            // 用于批量新增的数据列表
            List<CwMnlrDay> addList = new ArrayList<>();

            // 获取字典配置并与已有数据做并集
            List<CwNameDict> dictList = nameDictService.queryList("mnlrDay");
            if (dictList != null && !dictList.isEmpty()) {
                for (CwNameDict dictItem : dictList) {
                    boolean exists = mnlrDays.stream().anyMatch(v -> v.getName().equals(dictItem.getName()));
                    if (!exists) {
                        CwMnlrDay newDay = new CwMnlrDay();
                        BeanUtils.copyProperties(dictItem, newDay);
                        newDay.setId(null);
                        newDay.setRecordTime(date);
                        // 默认成本设置为0
                        newDay.setCb(BigDecimal.ZERO);
                        newDay.setJg(BigDecimal.ZERO);
                        mnlrDays.add(newDay);
                        addList.add(newDay);
                    }
                }
            }
            
            if (mnlrDays != null && !mnlrDays.isEmpty()) {
                for (CwMnlrDay d : mnlrDays) {
                    switch (d.getType()) {
                        case "t":
                            d.setXl(new BigDecimal(tjkjsl));
                            calculateCost(d);
                            break;
                        case "l":
                            d.setXl(new BigDecimal(ljkjsl));
                            calculateCost(d);
                            break;
                        case "ljk":
                            d.setXl(new BigDecimal(ljkzhl));
                            calculateCost(d);
                            break;
                        case "mjk":
                            d.setXl(new BigDecimal(mjkzhl));
                            calculateCost(d);
                            break;
                        case "j":
                            d.setXl(new BigDecimal(tjkhjl));
                            calculateCost(d);
                            break;
                        case "y":
                            d.setXl(new BigDecimal(tjkhyl));
                            calculateCost(d);
                            break;
                    }
                }
                
                // 按照是否存在ID区分新增和更新
                List<CwMnlrDay> updateList = mnlrDays.stream()
                        .filter(v -> v.getId() != null)
                        .collect(Collectors.toList());

                if (!updateList.isEmpty()) {
                    log.info("更新产量数据：{}", updateList);
                    mnlrDayService.updateBatchById(updateList);
                }

                if (!addList.isEmpty()) {
                    log.info("新增产量数据：{}", addList);
                    mnlrDayService.saveBatch(addList);
                }
            } else {
                log.warn("{}没有价格数据，无法更新产量", dateStr);
            }
        } else {
            log.warn("获取产量数据失败或为空：{}", cjJsonObj != null ? cjJsonObj.getString("Message") : "响应为空");
        }
    }

    /**
     * 获取当月1号的价格
     * @param type 金属类型
     * @param currentDate 当前日期
     * @return 当月1号的价格，如果没有找到则返回null
     */
    private BigDecimal getMonthFirstDayPrice(String type, Date currentDate) {
        // 计算当月1号
        Date firstDayOfMonth = DateUtil.beginOfMonth(currentDate);

        // 查询当月1号该类型的价格数据
        List<CwMnlrDay> firstDayData = mnlrDayService.lambdaQuery()
                .eq(CwMnlrDay::getType, type)
                .between(CwMnlrDay::getRecordTime, DateUtil.beginOfDay(firstDayOfMonth), DateUtil.endOfDay(firstDayOfMonth))
                .list();

        if (firstDayData != null && !firstDayData.isEmpty()) {
            CwMnlrDay firstDay = firstDayData.get(0);
            if (firstDay.getJg() != null) {
                log.info("获取到{}当月1号({})的价格: {}", type, DateUtil.format(firstDayOfMonth, "yyyy-MM-dd"), firstDay.getJg());
                return firstDay.getJg();
            }
        }

        log.warn("未找到{}当月1号({})的价格数据", type, DateUtil.format(firstDayOfMonth, "yyyy-MM-dd"));
        return null;
    }

    /**
     * 计算成本
     * @param day 数据对象
     */
    private void calculateCost(CwMnlrDay day) {
        if (day.getJg() != null && day.getXl() != null) {
            // 获取系数
            BigDecimal xs = CwMnlrDay.getXs(day.getType());
            // 计算成本 = 价格 * 系数 * 销量 / 1.13 / 10000
            BigDecimal cb = day.getJg()
                    .multiply(xs)
                    .multiply(day.getXl())
                    .divide(new BigDecimal("1.13"), 6, BigDecimal.ROUND_HALF_UP)
                    .divide(new BigDecimal("10000"), 6, BigDecimal.ROUND_HALF_UP);
            day.setCb(cb);
            log.info("计算{}成本: 价格={}, 系数={}, 销量={}, 成本={}",
                    day.getName(), day.getJg(), xs, day.getXl(), cb);
        } else {
            day.setCb(BigDecimal.ZERO);
            log.warn("无法计算成本，价格或销量为空: {}", day.getName());
        }
    }

    @Override
    public void getHistoryPriceData(Date startDate, Date endDate) {
        String startDateStr = DateUtil.format(startDate, "yyyy-MM-dd");
        String endDateStr = DateUtil.format(endDate, "yyyy-MM-dd");
        log.info("开始获取{}至{}的历史价格数据", startDateStr, endDateStr);
        
        try {
            // 获取token
            String token = getToken();
            if (ObjectUtil.isEmpty(token)) {
                return;
            }
            
            // 当前处理日期
            Date currentDate = startDate;
            
            // 遍历日期范围
            while (!currentDate.after(endDate)) {
                String dateStr = DateUtil.format(currentDate, "yyyy-MM-dd");
                log.info("处理{}的历史价格数据", dateStr);
                
                // 判断是否为周末，如果是周末，则使用上一个工作日(周五)的日期
                DateTime dateTime = DateUtil.parseDate(dateStr);
                int dayOfWeek = dateTime.dayOfWeek();
                Date queryDate = currentDate;
                
                // dayOfWeek: 1表示周日，2表示周一，...，7表示周六
                if (dayOfWeek == 7) { // 周六
                    queryDate = DateUtil.offsetDay(currentDate, -1); // 使用周五的日期
                    log.info("当天为周六，使用周五({})的价格数据", DateUtil.format(queryDate, "yyyy-MM-dd"));
                } else if (dayOfWeek == 1) { // 周日
                    queryDate = DateUtil.offsetDay(currentDate, -2); // 使用周五的日期
                    log.info("当天为周日，使用周五({})的价格数据", DateUtil.format(queryDate, "yyyy-MM-dd"));
                }
                
                // 分页获取当天数据
                int offset = 0;
                boolean hasMore = true;
                
                while (hasMore) {
                    // 创建请求参数，需要分页参数
                    DtsmmParam dtsmmParam = DtsmmParam.builder()
                            .reportDate(queryDate)
                            .pageSize(MAX_PAGE_SIZE)
                            .offSet(offset)
                            .build();
                    
                    // 使用JSON.toJSONString时确保DtsmmParam中的Date字段正确序列化
                    String jsonParams = JSON.toJSONString(dtsmmParam);
                    log.debug("请求参数: {}", jsonParams);
                    
                    // 发送请求
                    String dtsmmStr = HttpUtil.createPost(PRICE_URL)
                            .header("Content-Type", "application/json")
                            .auth(token)
                            .body(jsonParams)
                            .execute()
                            .body();
                    
                    // 解析结果
                    JSONObject dtsmmObj = JSONObject.parseObject(dtsmmStr);
                    if (ObjectUtil.isNotEmpty(dtsmmObj) && dtsmmObj.getBoolean("success")) {
                        JSONArray dataArray = dtsmmObj.getJSONArray("data");
                        if (ObjectUtil.isNotEmpty(dataArray) && dataArray.size() > 1) {
                            // 获取记录数信息
                            JSONArray countInfo = dataArray.getJSONArray(1);
                            if (countInfo != null && !countInfo.isEmpty()) {
                                JSONObject totalObj = countInfo.getJSONObject(0);
                                int totalNum = Integer.parseInt(totalObj.getString("total_num"));
                                
                                // 处理当前页数据，使用原始日期保存记录
                                processPriceData(dtsmmStr, currentDate);
                                
                                // 判断是否有更多数据
                                offset += MAX_PAGE_SIZE;
                                hasMore = offset < totalNum;
                                
                                if (hasMore) {
                                    // 重新获取token，避免过期
                                    token = getToken();
                                    if (ObjectUtil.isEmpty(token)) {
                                        break;
                                    }
                                }
                            } else {
                                hasMore = false;
                            }
                        } else {
                            hasMore = false;
                        }
                    } else {
                        log.error("获取{}的历史价格数据失败：{}", dateStr, dtsmmObj != null ? dtsmmObj.getString("errorMsg") : "响应为空");
                        hasMore = false;
                    }
                }
                
                // 处理下一天
                currentDate = DateUtil.offsetDay(currentDate, 1);
            }
            
            log.info("完成获取{}至{}的历史价格数据", startDateStr, endDateStr);
        } catch (Exception e) {
            log.error("获取历史价格数据异常", e);
        }
    }

    @Override
    public void getHistoryOutputData(Date startDate, Date endDate) {
        String startDateStr = DateUtil.format(startDate, "yyyy-MM-dd");
        String endDateStr = DateUtil.format(endDate, "yyyy-MM-dd");
        log.info("开始获取{}至{}的历史产量数据", startDateStr, endDateStr);
        
        try {
            // 查询指定日期范围内有价格数据的日期
            List<Date> priceDates = mnlrDayService.lambdaQuery()
                    .between(CwMnlrDay::getRecordTime, DateUtil.beginOfDay(startDate), DateUtil.endOfDay(endDate))
                    .groupBy(CwMnlrDay::getRecordTime)
                    .list()
                    .stream()
                    .map(CwMnlrDay::getRecordTime)
                    .distinct()
                    .toList();
            
            if (priceDates.isEmpty()) {
                log.warn("{}至{}没有价格数据，请先获取历史价格数据", startDateStr, endDateStr);
                return;
            }
            
            // 遍历所有有价格数据的日期
            for (Date date : priceDates) {
                String dateStr = DateUtil.format(date, "yyyy-MM-dd");
                log.info("获取{}的历史产量数据", dateStr);
                
                // 调用产量获取接口
                getOutputDataAndUpdate(date);
            }
            
            log.info("完成获取{}至{}的历史产量数据", startDateStr, endDateStr);
        } catch (Exception e) {
            log.error("获取历史产量数据异常", e);
        }
    }
    
    /**
     * 获取token
     * @return token字符串
     */
    private String getToken() {
        try {
            String tokenStr = HttpUtil.get(TOKEN_URL);
            log.info("获取token响应：{}", tokenStr);
            JSONObject tokenObj = JSONObject.parseObject(tokenStr);
            
            if (ObjectUtil.isNotEmpty(tokenObj) && tokenObj.containsKey("data")) {
                String token = tokenObj.getString("data");
                if (ObjectUtil.isEmpty(token)) {
                    log.error("获取token失败: token为空");
                    return null;
                }
                return token;
            } else {
                log.error("获取token失败: {}", tokenStr);
                return null;
            }
        } catch (Exception e) {
            log.error("获取token异常", e);
            return null;
        }
    }
} 