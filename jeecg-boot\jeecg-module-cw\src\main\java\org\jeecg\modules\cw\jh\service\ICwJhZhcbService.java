package org.jeecg.modules.cw.jh.service;



import org.jeecg.modules.cw.jh.param.CwJhZhcbSumbitParam;
import org.jeecg.modules.cw.jh.result.CwJhZhcbListResult;
import org.jeecg.modules.cw.krb.entity.CwKrbRow;

import java.util.Date;
import java.util.List;

public interface ICwJhZhcbService {

    CwJhZhcbListResult query(Date queryDate);

    void submit(CwJhZhcbSumbitParam param);

    List<CwKrbRow> sumByMonth(Date queryDate);

    CwJhZhcbListResult autoFill(Date queryDate);
}
