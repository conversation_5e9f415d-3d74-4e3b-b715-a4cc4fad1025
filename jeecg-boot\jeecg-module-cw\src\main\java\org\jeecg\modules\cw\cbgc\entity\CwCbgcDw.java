package org.jeecg.modules.cw.cbgc.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 成本构成（按单位）
 * @Author: jeecg-boot
 * @Date:   2025-02-20
 * @Version: V1.0
 */
@Data
@TableName("cw_cbgc_dw")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cw_cbgc_dw对象", description="成本构成（按单位）")
public class CwCbgcDw implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**法人单位*/
	@Excel(name = "法人单位", width = 15)
    @ApiModelProperty(value = "法人单位")
    private java.math.BigDecimal frdw;
	/**采矿场*/
	@Excel(name = "采矿场", width = 15)
    @ApiModelProperty(value = "采矿场")
    private java.math.BigDecimal ckc;
	/**大山厂*/
	@Excel(name = "大山厂", width = 15)
    @ApiModelProperty(value = "大山厂")
    private java.math.BigDecimal ds;
	/**泗选厂*/
	@Excel(name = "泗选厂", width = 15)
    @ApiModelProperty(value = "泗选厂")
    private java.math.BigDecimal sx;
	/**精尾厂*/
	@Excel(name = "精尾厂", width = 15)
    @ApiModelProperty(value = "精尾厂")
    private java.math.BigDecimal jw;
	/**新技术*/
	@Excel(name = "新技术", width = 15)
    @ApiModelProperty(value = "新技术")
    private java.math.BigDecimal xjs;
	/**检化*/
	@Excel(name = "检化", width = 15)
    @ApiModelProperty(value = "检化")
    private java.math.BigDecimal jh;
	/**时间*/
	@Excel(name = "时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "时间")
    private java.util.Date recordTime;
	/**单位*/
	@Excel(name = "单位", width = 15)
    @ApiModelProperty(value = "单位")
    private java.lang.String unit;
}
