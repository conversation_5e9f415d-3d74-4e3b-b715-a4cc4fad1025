package org.jeecg.modules.cw.mnlr.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.cw.cbwc.param.CwCbwcSumbitParam;
import org.jeecg.modules.cw.cbwc.result.CwCbwcQueryResult;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrMonth;
import org.jeecg.modules.cw.mnlr.param.CwMnlrMonthSumbitParam;
import org.jeecg.modules.cw.mnlr.result.CwMnlrMonthQueryResult;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrMonthService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 矿模拟利润（月）
 * @Author: jeecg-boot
 * @Date:   2025-01-07
 * @Version: V1.0
 */
@Api(tags="矿模拟利润（月）")
@RestController
@RequestMapping("/mnlr/cwMnlrMonth")
@Slf4j
public class CwMnlrMonthController extends JeecgController<CwMnlrMonth, ICwMnlrMonthService> {
	@Autowired
	private ICwMnlrMonthService cwMnlrMonthService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwMnlrMonth
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "矿模拟利润（月）-分页列表查询")
	@ApiOperation(value="矿模拟利润（月）-分页列表查询", notes="矿模拟利润（月）-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CwMnlrMonth>> queryPageList(CwMnlrMonth cwMnlrMonth,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwMnlrMonth> queryWrapper = QueryGenerator.initQueryWrapper(cwMnlrMonth, req.getParameterMap());
		Page<CwMnlrMonth> page = new Page<CwMnlrMonth>(pageNo, pageSize);
		IPage<CwMnlrMonth> pageList = cwMnlrMonthService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwMnlrMonth
	 * @return
	 */
	@AutoLog(value = "矿模拟利润（月）-添加")
	@ApiOperation(value="矿模拟利润（月）-添加", notes="矿模拟利润（月）-添加")
	@RequiresPermissions("mnlr:cw_mnlr_month:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwMnlrMonth cwMnlrMonth) {
		cwMnlrMonthService.save(cwMnlrMonth);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwMnlrMonth
	 * @return
	 */
	@AutoLog(value = "矿模拟利润（月）-编辑")
	@ApiOperation(value="矿模拟利润（月）-编辑", notes="矿模拟利润（月）-编辑")
	@RequiresPermissions("mnlr:cw_mnlr_month:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwMnlrMonth cwMnlrMonth) {
		cwMnlrMonthService.updateById(cwMnlrMonth);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "矿模拟利润（月）-通过id删除")
	@ApiOperation(value="矿模拟利润（月）-通过id删除", notes="矿模拟利润（月）-通过id删除")
	@RequiresPermissions("mnlr:cw_mnlr_month:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwMnlrMonthService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "矿模拟利润（月）-批量删除")
	@ApiOperation(value="矿模拟利润（月）-批量删除", notes="矿模拟利润（月）-批量删除")
	@RequiresPermissions("mnlr:cw_mnlr_month:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwMnlrMonthService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "矿模拟利润（月）-通过id查询")
	@ApiOperation(value="矿模拟利润（月）-通过id查询", notes="矿模拟利润（月）-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwMnlrMonth> queryById(@RequestParam(name="id",required=true) String id) {
		CwMnlrMonth cwMnlrMonth = cwMnlrMonthService.getById(id);
		if(cwMnlrMonth==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwMnlrMonth);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwMnlrMonth
    */
    @RequiresPermissions("mnlr:cw_mnlr_month:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwMnlrMonth cwMnlrMonth) {
        return super.exportXls(request, cwMnlrMonth, CwMnlrMonth.class, "矿模拟利润（月）");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("mnlr:cw_mnlr_month:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwMnlrMonth.class);
    }

	 @ApiOperation(value = "矿模拟利润（月）-列表查询", notes = "矿模拟利润（月）-列表查询")
	 @GetMapping(value = "/query")
	 public Result<CwMnlrMonthQueryResult> query(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
		 CwMnlrMonthQueryResult result = cwMnlrMonthService.queryByDate(queryDate);
		 return Result.ok(result);
	 }

	 @ApiOperation(value = "矿模拟利润（月）-提交", notes = "矿模拟利润（月）-提交")
	 @PostMapping(value = "/submit")
	 public Result<String> submit(@RequestBody CwMnlrMonthSumbitParam submitParam) {
		 cwMnlrMonthService.submit(submitParam);
		 return Result.OK("提交成功");
	 }
}
