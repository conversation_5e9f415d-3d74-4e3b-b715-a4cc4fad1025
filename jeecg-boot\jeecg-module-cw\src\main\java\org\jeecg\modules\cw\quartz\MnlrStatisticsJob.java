package org.jeecg.modules.cw.quartz;

import cn.hutool.core.date.DateUtil;
import lombok.extern.log4j.Log4j2;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrStatisticsDay;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrDayService;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrStatisticsDayService;
import org.jeecg.modules.cw.mnlr.result.CwMnlrDayQueryResult;
import org.jeecg.modules.cw.mnlr.util.MnlrStatisticsUtil;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 模拟利润统计定时任务
 * 自动计算模拟利润相关数据并保存到统计表中
 *
 * <AUTHOR>
 */
@Log4j2
@Service
public class MnlrStatisticsJob implements Job {
    @Resource
    private ICwMnlrDayService mnlrDayService;
    @Resource
    private ICwMnlrStatisticsDayService mnlrStatisticsDayService;

    @Override
    public void execute(JobExecutionContext ctx) throws JobExecutionException {
        try {
            log.info("开始执行模拟利润统计定时任务");
            Date today = DateUtil.beginOfDay(new Date());

            // 获取当天的模拟利润数据
            CwMnlrDayQueryResult result = mnlrDayService.queryByDate(today);
            if (result == null || result.getRows() == null || result.getRows().isEmpty()) {
                log.warn("当天没有模拟利润数据，任务结束");
                return;
            }

            // 使用工具类计算模拟利润统计数据
            CwMnlrStatisticsDay statisticsDay = MnlrStatisticsUtil.calculateStatistics(result, today);
            if (statisticsDay == null) {
                log.warn("计算模拟利润统计数据失败，任务结束");
                return;
            }

            // 保存或更新统计数据
            mnlrStatisticsDayService.addOrEdit(statisticsDay);

            log.info("模拟利润统计定时任务执行完成，日期：{}", DateUtil.formatDate(today));
        } catch (Exception e) {
            log.error("模拟利润统计定时任务执行异常", e);
            throw new JobExecutionException(e);
        }
    }

}