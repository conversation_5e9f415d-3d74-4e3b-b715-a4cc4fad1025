package org.jeecg.modules.cw.statistics.result;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 单位成本柱状图结果
 * 实际/计划总成本及吨矿成本
 */
@Data
public class CwUnitCostBarResult {
    /** 单位名称，如：采矿场、大山厂、泗选厂、精尾厂 */
    private String unit;

    /** 实际总成本（万元） */
    private BigDecimal actualTotalCost;

    /** 计划总成本（万元） */
    private BigDecimal planTotalCost;

    /** 实际吨矿成本（元/吨或万元/吨，取决于前端展示） */
    private BigDecimal actualTonCost;

    /** 计划吨矿成本 */
    private BigDecimal planTonCost;
} 