# 综合成本模块处理量/采拨量单位说明

> 版本：2025-07-01  
> 作者：系统自动生成

---

## 1. 背景

原系统中"处理量（cll）/采拨总量（cbzl）/处理量预测（cllyc）"在不同环节使用了 **吨** 与 **万吨** 混合的计量单位，导致前端展示与后台存储存在 *×1 000 / ×10 000* 的差值问题。现已统一为 **万吨**。

---

## 2. 单位统一方案

| 环节 | 调整前 | 调整后 |
| ---- | ------ | ------ |
| 接口 `PullCllCbzlJob` 获取外部数据 | 吨 | 吨 → **÷10000** → 万吨入库 |
| 数据库 `cw_cll_data.data / cw_cllyc_data.cllyc` | 吨 / 万吨混用 | **全部按万吨保存** |
| 综合成本 ServiceImpl（ckc / ds / sx / jw）查询 | 读取后 ÷10000 | **直接读取万屯值** |
| 综合成本 ServiceImpl 提交 | 写库前 ×10000 | **直接以万吨存库** |
| 前端展示 / 用户输入 | 万吨 | **万吨（保持不变）** |

---

## 3. 涉及代码

1. **定时任务** `PullCllCbzlJob`
   * 接口返回吨 → `divide(10000)` 后存库。
2. **综合成本实现类**  
   * `CwCkcZhcbServiceImpl`  
   * `CwDsZhcbServiceImpl`  
   * `CwJwZhcbServiceImpl`  
   * `CwSxZhcbServiceImpl`
   * 移除所有 `×/÷ 10000` 转换，直接以万吨读写。

---

## 4. 兼容性

- 历史数据已通过脚本统一转换为万吨。
- 其他报表、统计类服务如仍按吨读取，请相应调整。

---

## 5. 验证方法

1. 执行 `PullCllCbzlJob`，检查 `cw_cll_data.data` 是否为 **万**吨级别（例如 1.2345 代表 1.2345 万吨）。
2. 在综合成本页面填报数值（单位：万吨），保存后直接查询数据库，数值应保持一致。
3. 查看综合成本查询接口返回，值与数据库保持一致。

---

## 6. 版本记录

| 日期 | 修改人 | 说明 |
| ---- | ------ | ---- |
| 2025-07-01 | 系统 | 首次创建，统一单位为万吨 | 