package org.jeecg.modules.cw.ckc.mapper;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.cw.ckc.entity.CwCkcDay;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 采矿场-日填报
 * @Author: jeecg-boot
 * @Date: 2024-12-06
 * @Version: V1.0
 */
public interface CwCkcDayMapper extends BaseMapper<CwCkcDay> {
    
    /**
     * 查询采矿场日填报列表，不包含drs字段
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 采矿场日填报列表
     */
    List<CwCkcDay> selectCwCkcDayList(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
