package org.jeecg.modules.cw.sx.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.cw.sx.param.CwSxZhcbSumbitParam;
import org.jeecg.modules.cw.sx.result.CwSxZhcbListResult;
import org.jeecg.modules.cw.sx.service.ICwSxZhcbService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Description: 泗选厂-综合成本表
 * @Author: tang
 * @Date: 2024-12-06
 * @Version: V1.0
 */
@Api(tags = "泗选厂-综合财报")
@RestController
@RequestMapping("/sx/zhcb")
@Slf4j
public class CwSxZhcbController {

    @Resource
    private ICwSxZhcbService sxZhcbService;


    @ApiOperation(value = "泗选厂-综合成本-列表查询", notes = "泗选厂-综合成本-列表查询")
    @GetMapping(value = "/query")
    public Result<CwSxZhcbListResult> query(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        CwSxZhcbListResult result = sxZhcbService.query(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "泗选厂-综合成本-自动填充", notes = "泗选厂-综合成本-自动填充")
    @GetMapping(value = "/autoFill")
    public Result<CwSxZhcbListResult> autoFill(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        CwSxZhcbListResult result = sxZhcbService.autoFill(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "泗选厂-综合成本-提交", notes = "泗选厂-综合成本-提交")
    @PostMapping(value = "/submit")
    public Result<String> submit(@RequestBody CwSxZhcbSumbitParam submitParam) {
        sxZhcbService.submit(submitParam);
        return Result.OK("提交成功");
    }
}
