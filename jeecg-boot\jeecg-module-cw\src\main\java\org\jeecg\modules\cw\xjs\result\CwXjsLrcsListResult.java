package org.jeecg.modules.cw.xjs.result;

import lombok.Data;
import org.jeecg.modules.cw.xjs.entity.CwXjsLrcsRow;
import org.jeecg.modules.cw.xjs.entity.CwXjsLrcsXsRow;
import org.jeecg.modules.cw.xjs.entity.CwXjsRow;

import java.util.Date;
import java.util.List;

@Data
public class CwXjsLrcsListResult {
    private Date queryDate;
    private List<CwXjsLrcsRow> rows;
    private List<CwXjsLrcsXsRow> xsRows;
}
