package org.jeecg.modules.cw.qtfy.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.cw.ckc.param.CwCkcZhcbSumbitParam;
import org.jeecg.modules.cw.ckc.result.CwCkcZhcbListResult;
import org.jeecg.modules.cw.qtfy.entity.CwQtfy;
import org.jeecg.modules.cw.qtfy.param.CwQtfySumbitParam;
import org.jeecg.modules.cw.qtfy.result.CwQtfyListResult;
import org.jeecg.modules.cw.qtfy.service.ICwQtfyService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 其他费用
 * @Author: jeecg-boot
 * @Date:   2025-01-02
 * @Version: V1.0
 */
@Api(tags="其他费用")
@RestController
@RequestMapping("/qtfy/cwQtfy")
@Slf4j
public class CwQtfyController extends JeecgController<CwQtfy, ICwQtfyService> {
	@Autowired
	private ICwQtfyService cwQtfyService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwQtfy
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "其他费用-分页列表查询")
	@ApiOperation(value="其他费用-分页列表查询", notes="其他费用-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CwQtfy>> queryPageList(CwQtfy cwQtfy,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwQtfy> queryWrapper = QueryGenerator.initQueryWrapper(cwQtfy, req.getParameterMap());
		Page<CwQtfy> page = new Page<CwQtfy>(pageNo, pageSize);
		IPage<CwQtfy> pageList = cwQtfyService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwQtfy
	 * @return
	 */
	@AutoLog(value = "其他费用-添加")
	@ApiOperation(value="其他费用-添加", notes="其他费用-添加")
	@RequiresPermissions("qtfy:cw_qtfy:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwQtfy cwQtfy) {
		cwQtfyService.save(cwQtfy);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwQtfy
	 * @return
	 */
	@AutoLog(value = "其他费用-编辑")
	@ApiOperation(value="其他费用-编辑", notes="其他费用-编辑")
	@RequiresPermissions("qtfy:cw_qtfy:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwQtfy cwQtfy) {
		cwQtfyService.updateById(cwQtfy);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "其他费用-通过id删除")
	@ApiOperation(value="其他费用-通过id删除", notes="其他费用-通过id删除")
	@RequiresPermissions("qtfy:cw_qtfy:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwQtfyService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "其他费用-批量删除")
	@ApiOperation(value="其他费用-批量删除", notes="其他费用-批量删除")
	@RequiresPermissions("qtfy:cw_qtfy:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwQtfyService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "其他费用-通过id查询")
	@ApiOperation(value="其他费用-通过id查询", notes="其他费用-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwQtfy> queryById(@RequestParam(name="id",required=true) String id) {
		CwQtfy cwQtfy = cwQtfyService.getById(id);
		if(cwQtfy==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwQtfy);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwQtfy
    */
    @RequiresPermissions("qtfy:cw_qtfy:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwQtfy cwQtfy) {
        return super.exportXls(request, cwQtfy, CwQtfy.class, "其他费用");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("qtfy:cw_qtfy:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwQtfy.class);
    }


	 @ApiOperation(value = "其他费用-列表查询", notes = "其他费用-列表查询")
	 @GetMapping(value = "/query")
	 public Result<CwQtfyListResult> query(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
		 CwQtfyListResult result = cwQtfyService.query(queryDate);
		 return Result.ok(result);
	 }

	 @ApiOperation(value = "其他费用-其他费用", notes = "其他费用-其他费用")
	 @GetMapping(value = "/autoFill")
	 public Result<CwQtfyListResult> autoFill(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
		 CwQtfyListResult result = cwQtfyService.autoFill(queryDate);
		 return Result.ok(result);
	 }

	 @ApiOperation(value = "其他费用-提交", notes = "其他费用-提交")
	 @PostMapping(value = "/submit")
	 public Result<String> submit(@RequestBody CwQtfySumbitParam submitParam) {
		 cwQtfyService.submit(submitParam);
		 return Result.OK("提交成功");
	 }
}
