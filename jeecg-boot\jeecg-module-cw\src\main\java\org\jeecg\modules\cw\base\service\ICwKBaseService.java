package org.jeecg.modules.cw.base.service;

import cn.hutool.core.date.DateField;
import org.jeecg.modules.cw.base.entity.CwBaseData;
import com.baomidou.mybatisplus.extension.service.IService;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 其他基础数据
 * @Author: jeecg-boot
 * @Date: 2025-01-08
 * @Version: V1.0
 */
public interface ICwKBaseService extends IService<CwBaseData> {
    String getCwBaseDataYear(String name, Date queryDate);

    String getCwBaseDataMonth(String name, Date queryDate);

    String getCwBaseDataDay(String name, Date queryDate);

    void setCwBaseDataYear(String name, String data, Date queryDate);

    void setCwBaseDataMonth(String name, String data, Date queryDate);

    void setCwBaseDataDay(String name, String data, Date queryDate);

    BigDecimal getKzcb(Date queryDate);

    /**
     * 计算指定日期所有单位（ckc、ds、sx、jw、jh、jg）当日 drs 之和，作为总成本。
     * @param queryDate 日期
     * @return 当日 drs 总和
     */
    BigDecimal getTotalDrs(java.util.Date queryDate);

    void setCwBaseData(String name, String data, Date queryDate, DateField dateField);

    void deleteCwBaseData(String name, Date queryDate, DateField dateField);
}
