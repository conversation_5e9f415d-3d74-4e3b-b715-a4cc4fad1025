# 处理量和采拨量数据获取规则

## 背景

系统之前使用 `cw_cll_cbl_data` 表和 `cw_dw_base` 表存储处理量和采拨量数据。现在已经统一迁移到 `cw_cll_data` 表中，通过不同的 `type` 字段区分不同类型的数据。

## 数据结构

`cw_cll_data` 表主要字段说明：

- `id`: 主键
- `name`: 名称（可选）
- `type`: 数据类型，用于区分不同的数据
- `data`: 具体数值，以字符串形式存储
- `record_time`: 记录时间
- `unit`: 单位（可选）

## 数据类型说明

系统中使用 `CwCllDataName` 枚举类定义不同的数据类型：

```java
public enum CwCllDataName {
    /**
     * 泗州处理量
     */
    SZCLL("szcll", "SZCLL"),
    
    /**
     * 大山处理量
     */
    DSCLL("dscll", "DSCLL"),
    
    /**
     * 采拨总量
     */
    CBZL("cbzl", "CBZL");
    
    // ... 其他方法 ...
}
```

## 数据获取方法

通过 `ICwCllDataService` 接口提供的方法获取数据：

```java
// 获取单个日期的数据（推荐使用枚举类型）
String data = cwCllDataService.getCllData(CwCllDataName.CBZL, recordTime);

// 设置数据（推荐使用枚举类型）
cwCllDataService.setCllData(CwCllDataName.SZCLL, data, recordTime);

// 获取整月数据（推荐使用枚举类型）
Map<String, String> monthData = cwCllDataService.getMonthCllData(CwCllDataName.DSCLL, monthDate);

// 获取日期范围内的数据（推荐使用枚举类型）
Map<String, String> rangeData = cwCllDataService.getRangeCllData(CwCllDataName.CBZL, startDate, endDate);

// 以下是兼容旧版本的方法，不推荐使用
String data = cwCllDataService.getCllData("cbzl", recordTime);
cwCllDataService.setCllData("szcll", data, recordTime);
Map<String, String> monthData = cwCllDataService.getMonthCllData("dscll", monthDate);
Map<String, String> rangeData = cwCllDataService.getRangeCllData("cbzl", startDate, endDate);
```

## 示例代码

### 获取单个日期的数据

#### 获取采拨总量

```java
String cbzlStr = cwCllDataService.getCllData(CwCllDataName.CBZL, queryDate);
if (ObjectUtil.isNotEmpty(cbzlStr)) {
    BigDecimal cbzlValue = new BigDecimal(cbzlStr);
    // 如需转换单位（例如除以10000）
    cbzlValue = cbzlValue.divide(new BigDecimal(10000));
}
```

#### 获取泗州处理量

```java
String szcllStr = cwCllDataService.getCllData(CwCllDataName.SZCLL, queryDate);
if (ObjectUtil.isNotEmpty(szcllStr)) {
    BigDecimal szcllValue = new BigDecimal(szcllStr);
    // 进行后续处理
}
```

#### 获取大山处理量

```java
String dscllStr = cwCllDataService.getCllData(CwCllDataName.DSCLL, queryDate);
if (ObjectUtil.isNotEmpty(dscllStr)) {
    BigDecimal dscllValue = new BigDecimal(dscllStr);
    // 进行后续处理
}
```

### 获取批量数据

#### 获取整月采拨总量数据

```java
Map<String, String> cbzlDataMap = cwCllDataService.getMonthCllData(CwCllDataName.CBZL, monthDate);
Map<String, BigDecimal> result = new HashMap<>();

// 转换为BigDecimal
cbzlDataMap.forEach((dateStr, dataStr) -> {
    if (ObjectUtil.isNotEmpty(dataStr)) {
        result.put(dateStr, new BigDecimal(dataStr));
    }
});

// 使用数据
BigDecimal specificDateValue = result.get("2025-06-18");
```

#### 获取日期范围内的处理量数据

```java
// 获取指定日期范围内的泗州处理量数据
Map<String, String> szcllDataMap = cwCllDataService.getRangeCllData(CwCllDataName.SZCLL, startDate, endDate);
Map<String, BigDecimal> convertedMap = new HashMap<>();

// 转换为BigDecimal
if (ObjectUtil.isNotEmpty(szcllDataMap)) {
    szcllDataMap.forEach((dateStr, dataStr) -> {
        if (ObjectUtil.isNotEmpty(dataStr)) {
            convertedMap.put(dateStr, new BigDecimal(dataStr));
        }
    });
}
```

## 数据写入方法

### 写入采拨总量

```java
BigDecimal cbzlValue = new BigDecimal("1000000");
cwCllDataService.setCllData(CwCllDataName.CBZL, cbzlValue.toString(), recordTime);
```

### 写入处理量

```java
BigDecimal szcllValue = new BigDecimal("500000");
cwCllDataService.setCllData(CwCllDataName.SZCLL, szcllValue.toString(), recordTime);

BigDecimal dscllValue = new BigDecimal("300000");
cwCllDataService.setCllData(CwCllDataName.DSCLL, dscllValue.toString(), recordTime);
```

## 优化建议

1. 始终使用枚举类型 `CwCllDataName` 而不是字符串，这样可以避免拼写错误和类型不安全的问题。

2. 当需要获取大量日期的数据时，使用 `getMonthCllData` 或 `getRangeCllData` 方法，而不是循环调用 `getCllData`，这样可以减少数据库查询次数，提高性能。

3. 示例代码：从单次查询改为批量查询

```java
// 旧方法：逐日查询，性能较差
Map<String, BigDecimal> result = new HashMap<>();
DateTime tempDate = DateUtil.beginOfDay(startDate);
while (tempDate.compareTo(endDate) <= 0) {
    String dateStr = DateUtil.format(tempDate, "yyyy-MM-dd");
    String cbzlStr = cwCllDataService.getCllData(CwCllDataName.CBZL, tempDate);
    
    if (ObjectUtil.isNotEmpty(cbzlStr)) {
        result.put(dateStr, new BigDecimal(cbzlStr));
    }
    
    tempDate = DateUtil.offsetDay(tempDate, 1);
}

// 新方法：一次查询所有数据，性能更好
Map<String, String> dataMap = cwCllDataService.getRangeCllData(CwCllDataName.CBZL, startDate, endDate);
Map<String, BigDecimal> result = new HashMap<>();
if (ObjectUtil.isNotEmpty(dataMap)) {
    dataMap.forEach((dateStr, dataStr) -> {
        if (ObjectUtil.isNotEmpty(dataStr)) {
            result.put(dateStr, new BigDecimal(dataStr));
        }
    });
}
```

## 注意事项

1. 数据以字符串形式存储在 `data` 字段中，读取后需要转换为需要的数据类型
2. 时间查询使用 `DateUtil.beginOfDay(date)` 确保时间精度一致
3. 使用 `ObjectUtil.isNotEmpty()` 检查返回值，避免空指针异常
4. 批量查询接口返回的是 `Map<String, String>`，需要根据业务需求进行类型转换
5. 如果需要添加新的数据类型，应该在 `CwCllDataName` 枚举类中添加新的枚举值，而不是直接使用字符串 