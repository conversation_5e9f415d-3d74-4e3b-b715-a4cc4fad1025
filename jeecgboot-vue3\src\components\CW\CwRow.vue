<template>
  <!-- 小计行 -->
  <div class="row subtotal">
    <!--  项目  -->
    <div>{{ typeName }}</div>
    <!--  单位  -->
    <div>万元</div>
    <div class="span-3">小计</div>
    <!--  单位成本  -->
    <div>{{ formatNumber(xj?.dwcb) }}</div>
    <!--  当日数  -->
    <div>{{ formatNumber(xj?.drs) }}</div>
    <!--  月累计  -->
    <div>{{ formatNumber(Number(xj?.ylj) + Number(xj?.drs)) }}</div>
    <!--  月预算/月预测（合并列）  -->
    <div>{{ formatNumber(isFirstHalf ? xj?.yys : xj?.yyc) }}</div>
    <!--  进度  -->
    <div>{{ formatNumber(xj?.jd) }}%</div>
  </div>
  <!--  详情行    -->
  <div class="row" v-for="item in rows" :key="item.name">
    <!--  项目  -->
    <div>{{ item.name }}</div>
    <!--  单位  -->
    <div>{{ item.unit }}</div>
    <!--  平均单耗  -->
    <div>{{ formatNumber(item.pjdh as number) }}</div>
    <!--  平均总耗  -->
    <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="item.pjzh" /></div>
    <!--  平均单价  -->
    <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="item.pjdj" /></div>
    <!--  单位成本  -->
    <div>{{ formatNumber(item?.dwcb as number) }}</div>
    <!--  当日数  -->
    <div>{{ formatNumber(item?.drs as number) }}</div>
    <!--  月累计  -->
    <div>{{ formatNumber(Number(item?.ylj) + Number(item?.drs)) }}</div>
    <!--  月预算/月预测（合并列）  -->
    <div>
      <template v-if="isFirstHalf">
        <template v-if="canEditYys">
          <a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="item.yys" />
        </template>
        <template v-else>
          {{ formatNumber(item?.yys as number) }}
        </template>
      </template>
      <template v-else>
        <template v-if="canEditYyc">
          <a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="item.yyc" />
        </template>
        <template v-else>
          {{ formatNumber(item?.yyc as number) }}
        </template>
      </template>
    </div>
    <!--  进度  -->
    <div>{{ formatNumber(item?.jd as number) }}%</div>
  </div>
</template>
<script setup lang="ts">
  import { computed, watch } from 'vue';
  import { CwBase, CWRow } from '#/cw';
  import { formatNumber, getSafeValue } from '@/utils/showUtils';
  import Big from 'big.js';

  const props = defineProps({
    rows: {
      type: Array<CWRow>,
      default: () => [],
    },
    base: {
      type: Object as PropType<CwBase>,
      default: () => {},
    },
    typeName: {
      type: String,
      required: true,
    },
    canEditYyc: {
      type: Boolean,
      default: false,
    },
    canEditYys: {
      type: Boolean,
      default: false,
    },
    isFirstHalf: {
      type: Boolean,
      default: false,
    },
  });
  // 处理行数据
  const xj = computed(() => {
    // 使用新的计算逻辑，不依赖平均单价和平均总耗
    const totalDrs = props.rows.reduce((acc, cur) => acc.plus(getSafeValue(cur.drs)), new Big(0));
    const totalYys = props.rows.reduce((acc, cur) => acc.plus(getSafeValue(cur.yys)), new Big(0));
    const totalYlj = props.rows.reduce((acc, cur) => acc.plus(getSafeValue(cur.ylj)), new Big(0));
    const totalYyc = props.rows.reduce((acc, cur) => acc.plus(getSafeValue(cur.yyc)), new Big(0));

    const baseXj = getSafeValue(props.base.xj);

    const dwcb = baseXj.eq(0) ? 0 : totalDrs.div(baseXj).toNumber();
    const drs = totalDrs.toNumber();
    const ylj = totalYlj.toNumber();
    const yys = totalYys.toNumber();
    const yyc = totalYyc.toNumber();
    const jd = totalYys.eq(0) ? 0 : (totalYlj.plus(totalDrs)).times(100).div(totalYys).toNumber(); // 进度 = 月累计 / 月预算

    return {
      dwcb: isNaN(dwcb) ? 0 : dwcb,
      drs: isNaN(drs) ? 0 : drs,
      ylj: isNaN(ylj) ? 0 : ylj,
      yys: isNaN(yys) ? 0 : yys,
      yyc: isNaN(yyc) ? 0 : yyc,
      jd: isNaN(jd) ? 0 : jd,
    };
  });

  props.rows.forEach((row) => {
    // 平均单耗不再参与实际计算，仅作为展示
    row.pjdh = computed(() => {
      const pjzh = getSafeValue(row.pjzh);
      const xq = getSafeValue(props.base.xq);
      const result = xq.eq(0) ? 0 : pjzh.div(xq).toNumber();
      return isNaN(result) ? 0 : result;
    });

    // 当日数计算
    row.drs = computed(() => {
      // 优先使用月预测 (yyc)，若无月预测则使用月预算 (yys)，都为空时回退到平均单价×平均总耗
      const yyc = getSafeValue(row.yyc);
      const yys = getSafeValue(row.yys);
      const cbzl = getSafeValue(props.base.xj);
      const cllyc = getSafeValue(props.base.cllyc);

      let primaryVal = new Big(0);
      if (props.isFirstHalf) {
        primaryVal = yys;
      } else {
        primaryVal = yyc;
      }

      if (primaryVal.gt(0) && cllyc.gt(0)) {
        const result = primaryVal.div(cllyc).times(cbzl).toNumber();
        return isNaN(result) ? 0 : result;
      }

      // 回退：使用平均单价 × 平均总耗
      const pjdj = getSafeValue(row.pjdj);
      const pjzh = getSafeValue(row.pjzh);
      const result = pjdj.times(pjzh).toNumber();
      return isNaN(result) ? 0 : result;
    });

    // 单位成本计算 e = 当日数 / 采剥总量
    row.dwcb = computed(() => {
      const drs = getSafeValue(row.drs);
      const cbzl = getSafeValue(props.base.xj);
      if (cbzl.eq(0)) return 0;
      const result = drs.div(cbzl).toNumber();
      return isNaN(result) ? 0 : result;
    });

    // 进度计算
    row.jd = computed(() => {
      const yys = getSafeValue(row.yys);
      const ylj = getSafeValue(row.ylj);
      const drs = getSafeValue(row.drs);
      const result = yys.eq(0) ? 0 : ylj.plus(drs).times(100).div(yys).toNumber();
      return isNaN(result) ? 0 : result;
    });

    // 注意：不需要计算lj，因为页面上显示的是后端返回的ylj
  });
  // 事件定义
  const emit = defineEmits(['change']);
  watch(
    () => props.rows,
    () => {
      emit('change', props.rows, xj);
    },
    { deep: true }
  );
  emit('change', props.rows, xj);
</script>
<style lang="less" scoped>
  @import url(@/views/cw/public/style/ant-input.css);

  /* 容器样式 */
  .table-container {
    width: 100%;
    //max-width: 1800px;
    margin: 0 auto;
    background: linear-gradient(180deg, #f5f7fa 0%, #eef1f5 100%);
    padding: 20px;
    border-radius: 12px;
    box-shadow:
      0 4px 6px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.08);
  }

  /* 表格基础样式 */
  .grid-table {
    display: grid;
    gap: 5px;
    border-radius: 6px;
    overflow: hidden;
    background-color: #ffffff; /* 确保表格内部有清晰对比 */
  }

  /* 行样式 */
  .row {
    display: contents; /* 让子元素直接成为网格项 */
  }

  .custom-center-text :deep(.ant-input-number-input) {
    text-align: center !important;
  }

  :deep(.custom-center-text) {
    text-align: center !important;
  }
  /* 表头样式 */
  .header {
    font-weight: bold;
    color: white;
    background: linear-gradient(135deg, #007bff, #1e90ff);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  /* 定义每一列的宽度 */
  .grid-table > div {
    display: grid;
    grid-template-columns: repeat(11, 1fr); /* 12 列 */
    padding: 10px;
    text-align: center;
    border-right: 1px solid #eaeaea;
    transition: background-color 0.2s;

    &:last-child {
      border-right: none;
    }
  }

  /* 单元格悬停效果 */
  .grid-table > div:hover {
    background-color: #e9ecef;
  }

  /* 小计单元格样式 */
  .subtotal .span-3 {
    grid-column: span 3; /* 跨越3列 */
    background-color: #e6ffed;
    color: #28a745;
    font-weight: bold;
  }

  /* 小计行项目名称样式 */
  .subtotal > div:first-child {
    font-weight: bold;
  }

  /* 行交替背景色 */
  .grid-table > :nth-child(even) {
    background-color: #f8f9fa;
  }

  .grid-table > :nth-child(odd) {
    background-color: #ffffff;
  }
</style>
