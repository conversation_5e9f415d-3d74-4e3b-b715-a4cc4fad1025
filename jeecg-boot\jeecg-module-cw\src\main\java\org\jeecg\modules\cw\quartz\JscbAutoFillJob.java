package org.jeecg.modules.cw.quartz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.log4j.Log4j2;
import org.jeecg.modules.cw.base.service.ICwCllCblDataService;
import org.jeecg.modules.cw.base.service.ICwKBaseService;
import org.jeecg.modules.cw.jscb.entity.CwJscb;
import org.jeecg.modules.cw.jscb.entity.CwJscbRow;
import org.jeecg.modules.cw.jscb.entity.JscbConst;
import org.jeecg.modules.cw.jscb.service.ICwJscbService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSONObject;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Log4j2
@Service
public class JscbAutoFillJob implements Job {

    @Resource
    private ICwJscbService jscbService;
    @Resource
    private ICwKBaseService cwKBaseService;


    @Override
    public void execute(JobExecutionContext ctx) throws JobExecutionException {
        /**
         * 自动填充每月的金属成本
         */
        Date date = new Date();
        try {
            // 从Job参数中获取参数
            String parameter = (String) ctx.getJobDetail().getJobDataMap().get("parameter");
            JSONObject paramJson = null;
            
            // 源月份和目标月份
            Date sourceMonth = null;
            Date targetMonth = null;
            
            if (ObjectUtil.isNotEmpty(parameter)) {
                paramJson = JSONObject.parseObject(parameter);
                
                // 从参数中获取源月份
                String sourceMonthStr = paramJson.getString("sourceMonth");
                if (ObjectUtil.isNotEmpty(sourceMonthStr)) {
                    sourceMonth = DateUtil.parse(sourceMonthStr, "yyyy-MM");
                    log.info("从参数中获取源月份: {}", sourceMonthStr);
                }
                
                // 从参数中获取目标月份
                String targetMonthStr = paramJson.getString("targetMonth");
                if (ObjectUtil.isNotEmpty(targetMonthStr)) {
                    targetMonth = DateUtil.parse(targetMonthStr, "yyyy-MM");
                    log.info("从参数中获取目标月份: {}", targetMonthStr);
                }
            }
            
            // 如果没有指定目标月份，默认为当前月
            if (targetMonth == null) {
                targetMonth = DateUtil.beginOfMonth(date);
                log.info("未指定目标月份，使用当前月: {}", DateUtil.format(targetMonth, "yyyy-MM"));
            }
            
            // 如果没有指定源月份，默认为上一个月
            if (sourceMonth == null) {
                sourceMonth = DateUtil.offsetMonth(targetMonth, -1);
                log.info("未指定源月份，使用上一个月: {}", DateUtil.format(sourceMonth, "yyyy-MM"));
            }

            // 分摊系数获取指定月份的数据
            List<CwJscb> jscbList = jscbService.lambdaQuery()
                    .ge(CwJscb::getRecordTime, DateUtil.beginOfMonth(sourceMonth))
                    .le(CwJscb::getRecordTime, DateUtil.endOfMonth(sourceMonth))
                    .list();
            
            // 如果指定源月份没有数据，尝试获取最近有数据的月份
            if (ObjectUtil.isEmpty(jscbList) && sourceMonth.equals(DateUtil.offsetMonth(targetMonth, -1))) {
                int offset = -2; // 从上上个月开始找
                Date offsetMonth = DateUtil.offsetMonth(targetMonth, offset);
                while (true) {
                    Long count = jscbService.lambdaQuery()
                            .ge(CwJscb::getRecordTime, DateUtil.beginOfMonth(offsetMonth))
                            .le(CwJscb::getRecordTime, DateUtil.endOfMonth(offsetMonth))
                            .count();
                    if (count > 0 || offset < -20) {
                        break;
                    }
                    offset--;
                    offsetMonth = DateUtil.offsetMonth(targetMonth, offset);
                }
                
                // 如果找到数据，更新源月份
                if (offset >= -20) {
                    sourceMonth = offsetMonth;
                    log.info("源月份没有数据，自动查找到有数据的月份: {}", DateUtil.format(sourceMonth, "yyyy-MM"));
                    
                    // 重新查询数据
                    jscbList = jscbService.lambdaQuery()
                            .ge(CwJscb::getRecordTime, DateUtil.beginOfMonth(sourceMonth))
                            .le(CwJscb::getRecordTime, DateUtil.endOfMonth(sourceMonth))
                            .list();
                }
            }
            
            // 更新时间并保存
            if (ObjectUtil.isNotEmpty(jscbList)) {
                // 计算月份差
                int monthDiff = (int)((targetMonth.getTime() - sourceMonth.getTime()) / (1000L * 60 * 60 * 24 * 30));
                
                for (CwJscb jscb : jscbList) {
                    DateTime recordTime = DateUtil.offsetMonth(jscb.getRecordTime(), monthDiff);
                    jscb.setRecordTime(recordTime);
                    jscb.setId(null);
                }
                jscbService.saveBatch(jscbList);
                log.info("成功从{}复制金属成本分摊系数到{}", 
                        DateUtil.format(sourceMonth, "yyyy-MM"), 
                        DateUtil.format(targetMonth, "yyyy-MM"));
            } else {
                log.warn("源月份{}没有金属成本分摊系数数据", DateUtil.format(sourceMonth, "yyyy-MM"));
            }
            
            // 其他系数获取指定月的数据
            BigDecimal dyf = new BigDecimal(
                    ObjectUtil.defaultIfNull(cwKBaseService.getCwBaseDataMonth(JscbConst.DYF, sourceMonth), "0"));
            BigDecimal zycb = new BigDecimal(
                    ObjectUtil.defaultIfNull(cwKBaseService.getCwBaseDataMonth(JscbConst.ZYCB, sourceMonth), "0"));
            BigDecimal msft = new BigDecimal(
                    ObjectUtil.defaultIfNull(cwKBaseService.getCwBaseDataMonth(JscbConst.MSFT, sourceMonth), "0"));
            BigDecimal clcb = new BigDecimal(
                    ObjectUtil.defaultIfNull(cwKBaseService.getCwBaseDataMonth(JscbConst.CLCB, sourceMonth), "0"));
            
            // 如果所有值都不为0，将找到的值填充到目标月份
            if (dyf.compareTo(BigDecimal.ZERO) > 0 && zycb.compareTo(BigDecimal.ZERO) > 0 
                    && msft.compareTo(BigDecimal.ZERO) > 0 && clcb.compareTo(BigDecimal.ZERO) > 0) {
                cwKBaseService.setCwBaseDataMonth(JscbConst.DYF, dyf.toString(), targetMonth);
                cwKBaseService.setCwBaseDataMonth(JscbConst.ZYCB, zycb.toString(), targetMonth);
                cwKBaseService.setCwBaseDataMonth(JscbConst.MSFT, msft.toString(), targetMonth);
                cwKBaseService.setCwBaseDataMonth(JscbConst.CLCB, clcb.toString(), targetMonth);
                log.info("金属成本基础值填充成功，来源月份：{}，目标月份：{}", 
                        DateUtil.format(sourceMonth, "yyyy-MM"), 
                        DateUtil.format(targetMonth, "yyyy-MM"));
            } else {
                // 如果指定的源月份没有数据，尝试自动查找有数据的月份
                if (sourceMonth.equals(DateUtil.offsetMonth(targetMonth, -1))) {
                    int offset = -2; // 从上上个月开始找
                    Date offsetMonth = DateUtil.offsetMonth(targetMonth, offset);
                    while (true) {
                        dyf = new BigDecimal(
                                ObjectUtil.defaultIfNull(cwKBaseService.getCwBaseDataMonth(JscbConst.DYF, offsetMonth), "0"));
                        zycb = new BigDecimal(
                                ObjectUtil.defaultIfNull(cwKBaseService.getCwBaseDataMonth(JscbConst.ZYCB, offsetMonth), "0"));
                        msft = new BigDecimal(
                                ObjectUtil.defaultIfNull(cwKBaseService.getCwBaseDataMonth(JscbConst.MSFT, offsetMonth), "0"));
                        clcb = new BigDecimal(
                                ObjectUtil.defaultIfNull(cwKBaseService.getCwBaseDataMonth(JscbConst.CLCB, offsetMonth), "0"));
                        
                        // 如果所有值都不为0，说明找到有效数据
                        if (dyf.compareTo(BigDecimal.ZERO) > 0 && zycb.compareTo(BigDecimal.ZERO) > 0 
                                && msft.compareTo(BigDecimal.ZERO) > 0 && clcb.compareTo(BigDecimal.ZERO) > 0) {
                            
                            // 更新找到的源月份
                            sourceMonth = offsetMonth;
                            log.info("自动找到包含金属成本基础值的月份: {}", DateUtil.format(sourceMonth, "yyyy-MM"));
                            
                            // 将找到的值填充到目标月份
                            cwKBaseService.setCwBaseDataMonth(JscbConst.DYF, dyf.toString(), targetMonth);
                            cwKBaseService.setCwBaseDataMonth(JscbConst.ZYCB, zycb.toString(), targetMonth);
                            cwKBaseService.setCwBaseDataMonth(JscbConst.MSFT, msft.toString(), targetMonth);
                            cwKBaseService.setCwBaseDataMonth(JscbConst.CLCB, clcb.toString(), targetMonth);
                            log.info("金属成本基础值填充成功，来源月份：{}，目标月份：{}", 
                                    DateUtil.format(sourceMonth, "yyyy-MM"), 
                                    DateUtil.format(targetMonth, "yyyy-MM"));
                            break;
                        }
                        
                        offset--;
                        // 限制最多查询20个月前的数据
                        if (offset < -20) {
                            log.warn("未找到有效的金属成本基础值，查询已超过20个月");
                            break;
                        }
                        offsetMonth = DateUtil.offsetMonth(targetMonth, offset);
                    }
                } else {
                    log.warn("指定源月份{}无有效的金属成本基础值", DateUtil.format(sourceMonth, "yyyy-MM"));
                }
            }
            
            log.info("金属成本填充完成，从{}复制到{}", 
                    DateUtil.format(sourceMonth, "yyyy-MM"), 
                    DateUtil.format(targetMonth, "yyyy-MM"));
        } catch (Exception e) {
            log.error("自动填充金属成本失败", e);
        }
    }

}
