package org.jeecg.modules.cw.cbwc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.cbwc.entity.CwCbwc;
import org.jeecg.modules.cw.cbwc.entity.CwCbwcRow;
import org.jeecg.modules.cw.cbwc.mapper.CwCbwcMapper;
import org.jeecg.modules.cw.cbwc.param.CwCbwcSumbitParam;
import org.jeecg.modules.cw.cbwc.result.CwCbwcQueryResult;
import org.jeecg.modules.cw.cbwc.service.ICwCbwcService;
import org.jeecg.modules.cw.ckc.service.ICwCkcZhcbService;
import org.jeecg.modules.cw.ds.service.ICwDsZhcbService;
import org.jeecg.modules.cw.jw.service.ICwJwZhcbService;
import org.jeecg.modules.cw.sx.service.ICwSxZhcbService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: 成本完成表
 * @Author: jeecg-boot
 * @Date: 2024-12-31
 * @Version: V1.0
 */
@Service
public class CwCbwcServiceImpl extends ServiceImpl<CwCbwcMapper, CwCbwc> implements ICwCbwcService {

    private static final String DICT_TYPE = "cbwc";

    @Resource
    private ICwNameDictService nameDictService;
    @Resource
    private ICwCkcZhcbService ckcZhcbService;
    @Resource
    private ICwDsZhcbService dsZhcbService;
    @Resource
    private ICwSxZhcbService sxZhcbService;
    @Resource
    private ICwJwZhcbService jwZhcbService;

    @Override
    public CwCbwcQueryResult queryByDate(Date queryDate) {
        CwCbwcQueryResult result = new CwCbwcQueryResult();
        result.setQueryDate(queryDate);
        // 项目字典
        List<CwNameDict> dict = nameDictService.queryList(DICT_TYPE);
        // 已有列表
        List<CwCbwc> cb = this.lambdaQuery()
                .ge(CwCbwc::getRecordTime, DateUtil.beginOfMonth(queryDate))
                .le(CwCbwc::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();
        // 合并数据
        List<CwCbwcRow> resRows = new ArrayList<>();
        for (CwNameDict d : dict) {
            CwCbwcRow row = new CwCbwcRow();
            BeanUtil.copyProperties(d, row);
            // 数据
            cb.stream().filter((v) -> d.getName().equals(v.getName()))
                    .findFirst()
                    .ifPresent(v -> {
                        BeanUtil.copyProperties(v, row);
                    });
            // 实际成本
            BigDecimal sum = BigDecimal.ZERO;
            switch (d.getType()) {
                case "ckc":
                    sum = ckcZhcbService.sumDrs(queryDate);
                    break;
                case "ds":
                    sum = dsZhcbService.sumDrs(queryDate);
                    break;
                case "sx":
                    sum = sxZhcbService.sumDrs(queryDate);
                    break;
                case "jw":
                    sum = jwZhcbService.sumDrs(queryDate);
                    break;
            }
            row.setSjcb(sum);
            // 添加
            resRows.add(row);
        }
        result.setRows(resRows);
        // 结果
        return result;
    }

    @Override
    public void submit(CwCbwcSumbitParam param) {
        Date submitDate = param.getSubmitDate();
        // 更新行数据
        List<CwCbwcRow> rows = param.getRows();
        this.remove(new LambdaQueryWrapper<>(CwCbwc.class)
                .ge(CwCbwc::getRecordTime, DateUtil.beginOfMonth(submitDate))
                .le(CwCbwc::getRecordTime, DateUtil.endOfMonth(submitDate)));
        ArrayList<CwCbwc> months = new ArrayList<>();
        for (CwCbwcRow row : rows) {
            CwCbwc month = new CwCbwc();
            BeanUtil.copyProperties(row, month);
            month.setRecordTime(submitDate);
            months.add(month);
        }
        this.saveBatch(months);
    }

    @Override
    public CwCbwcQueryResult autoFill(Date queryDate) {
        // 从上个月开始，逐月回溯查找数据
        for (int i = 1; i < 12; i++) { // 最多回溯11次，即一年内
            Date dateToTry = DateUtil.offset(queryDate, DateField.MONTH, -i);
            long count = this.lambdaQuery()
                    .between(CwCbwc::getRecordTime, DateUtil.beginOfMonth(dateToTry), DateUtil.endOfMonth(dateToTry))
                    .count();
            if (count > 0) {
                // 找到数据，立即使用这个日期进行查询并返回
                return queryByDate(dateToTry);
            }
        }
        // 如果一年内都找不到数据，则默认查询当天
        return queryByDate(queryDate);
    }
}
