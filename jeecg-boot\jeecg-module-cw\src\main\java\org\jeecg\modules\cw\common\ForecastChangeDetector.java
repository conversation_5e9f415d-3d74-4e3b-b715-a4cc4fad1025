package org.jeecg.modules.cw.common;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 预测数据变更检测器
 * 用于检测月预算、上半月预测、下半月预测、月末预测的变更情况
 */
public class ForecastChangeDetector {
    
    /**
     * 预测数据变更结果
     */
    public static class ForecastChangeResult {
        private boolean monthlyBudgetChanged;      // 月预算是否变更
        private boolean firstHalfForecastChanged;  // 上半月预测是否变更
        private boolean secondHalfForecastChanged; // 下半月预测是否变更
        private boolean monthEndForecastChanged;   // 月末预测是否变更
        
        // 构造函数
        public ForecastChangeResult(boolean monthlyBudgetChanged, boolean firstHalfForecastChanged, 
                                  boolean secondHalfForecastChanged, boolean monthEndForecastChanged) {
            this.monthlyBudgetChanged = monthlyBudgetChanged;
            this.firstHalfForecastChanged = firstHalfForecastChanged;
            this.secondHalfForecastChanged = secondHalfForecastChanged;
            this.monthEndForecastChanged = monthEndForecastChanged;
        }
        
        /**
         * 是否需要重算整个月
         * 当月预算、上半月预测或下半月预测发生变更时，需要重算整个月
         */
        public boolean needsFullMonthRecalculation() {
            return monthlyBudgetChanged || firstHalfForecastChanged || secondHalfForecastChanged;
        }
        
        /**
         * 是否只需要重算月末最后一天
         * 仅当只有月末预测发生变更，且其他预测数据未变更时
         */
        public boolean needsOnlyLastDayRecalculation() {
            return monthEndForecastChanged && !needsFullMonthRecalculation();
        }
        
        /**
         * 是否有任何预测数据变更
         */
        public boolean hasAnyChange() {
            return monthlyBudgetChanged || firstHalfForecastChanged || 
                   secondHalfForecastChanged || monthEndForecastChanged;
        }
        
        // Getters
        public boolean isMonthlyBudgetChanged() { return monthlyBudgetChanged; }
        public boolean isFirstHalfForecastChanged() { return firstHalfForecastChanged; }
        public boolean isSecondHalfForecastChanged() { return secondHalfForecastChanged; }
        public boolean isMonthEndForecastChanged() { return monthEndForecastChanged; }
    }
    
    /**
     * 检测预测数据变更
     * @param oldYys 原月预算
     * @param newYys 新月预算
     * @param oldFirstHalf 原上半月预测
     * @param newFirstHalf 新上半月预测
     * @param oldSecondHalf 原下半月预测
     * @param newSecondHalf 新下半月预测
     * @param oldMonthEnd 原月末预测
     * @param newMonthEnd 新月末预测
     * @return 变更检测结果
     */
    public static ForecastChangeResult detectChanges(
            BigDecimal oldYys, BigDecimal newYys,
            BigDecimal oldFirstHalf, BigDecimal newFirstHalf,
            BigDecimal oldSecondHalf, BigDecimal newSecondHalf,
            BigDecimal oldMonthEnd, BigDecimal newMonthEnd) {
        
        boolean monthlyBudgetChanged = !Objects.equals(oldYys, newYys);
        boolean firstHalfForecastChanged = !Objects.equals(oldFirstHalf, newFirstHalf);
        boolean secondHalfForecastChanged = !Objects.equals(oldSecondHalf, newSecondHalf);
        boolean monthEndForecastChanged = !Objects.equals(oldMonthEnd, newMonthEnd);
        
        return new ForecastChangeResult(monthlyBudgetChanged, firstHalfForecastChanged, 
                                      secondHalfForecastChanged, monthEndForecastChanged);
    }
    
    /**
     * 比较两个BigDecimal值是否相等（处理null情况）
     */
    private static boolean isEqual(BigDecimal val1, BigDecimal val2) {
        if (val1 == null && val2 == null) return true;
        if (val1 == null || val2 == null) return false;
        return val1.compareTo(val2) == 0;
    }
}
