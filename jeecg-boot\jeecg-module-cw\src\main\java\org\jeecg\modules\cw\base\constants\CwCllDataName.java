package org.jeecg.modules.cw.base.constants;

/**
 * 处理量数据名称枚举
 * 用于定义cw_cll_data表中的name字段的可能值
 */
public enum CwCllDataName {
    /**
     * 泗州处理量
     */
    SZCLL("szcll", "SZCLL"),
    
    /**
     * 大山处理量
     */
    DSCLL("dscll", "DSCLL"),
    
    /**
     * 采拨总量
     */
    CBZL("cbzl", "CBZL");
    
    /**
     * 类型代码
     */
    private final String code;
    
    /**
     * 类型名称
     */
    private final String name;
    
    CwCllDataName(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    /**
     * 获取类型代码
     * @return 类型代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取类型名称
     * @return 类型名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 根据代码获取枚举
     * @param code 类型代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static CwCllDataName getByCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (CwCllDataName type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        
        return null;
    }
    
    @Override
    public String toString() {
        return code;
    }
} 