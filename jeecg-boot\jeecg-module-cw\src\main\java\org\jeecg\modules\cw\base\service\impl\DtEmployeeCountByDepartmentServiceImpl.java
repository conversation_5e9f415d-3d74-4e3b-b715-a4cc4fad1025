package org.jeecg.modules.cw.base.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.cw.base.entity.DtEmployeeCountByDepartment;
import org.jeecg.modules.cw.base.mapper.DtEmployeeCountByDepartmentMapper;
import org.jeecg.modules.cw.base.service.IDtEmployeeCountByDepartmentService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * Service implementation for 德铜人员统计表
 */
@Service
public class DtEmployeeCountByDepartmentServiceImpl
        extends ServiceImpl<DtEmployeeCountByDepartmentMapper, DtEmployeeCountByDepartment>
        implements IDtEmployeeCountByDepartmentService {

    @Resource
    private DtEmployeeCountByDepartmentMapper employeeCountMapper;

    @Override
    public Integer getTotalEmployeeCount(Date startDate, Date endDate) {
        return employeeCountMapper.totalEmployeeCount(startDate, endDate);
    }

    @Override
    public Integer getFilteredEmployeeCountForMonth(Date startDate, Date endDate) {
        return employeeCountMapper.getFilteredEmployeeCountForMonth(startDate, endDate);
    }

    @Override
    public Integer getFilteredEmployeeCountForYear(Date startDate, Date endDate) {
        return employeeCountMapper.getFilteredEmployeeCountForYear(startDate, endDate);
    }
} 