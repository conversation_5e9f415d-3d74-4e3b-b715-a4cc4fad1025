package org.jeecg.modules.cw.krb.controller;

import java.util.*;

import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.cw.krb.entity.CwKrb;
import org.jeecg.modules.cw.krb.param.CwKrbSumbitParam;
import org.jeecg.modules.cw.krb.result.CwKrbQueryResult;
import org.jeecg.modules.cw.krb.service.ICwKrbService;

import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: 矿日报表
 * @Author: jeecg-boot
 * @Date: 2024-12-31
 * @Version: V1.0
 */
@Api(tags = "矿日报表")
@RestController
@RequestMapping("/krb/cwKrb")
@Slf4j
public class CwKrbController extends JeecgController<CwKrb, ICwKrbService> {
    @Autowired
    private ICwKrbService cwKrbService;

    @ApiOperation(value = "矿日报表-列表查询", notes = "矿日报表-列表查询")
    @GetMapping(value = "/query")
    public Result<CwKrbQueryResult> query(@DateTimeFormat(pattern = "yyyy-MM-dd") Date queryDate) {
        CwKrbQueryResult result = cwKrbService.queryByDate(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "矿日报表-提交", notes = "矿日报表-提交")
    @PostMapping(value = "/submit")
    public Result<String> submit(@RequestBody CwKrbSumbitParam submitParam) {
        cwKrbService.submit(submitParam);
        return Result.OK("提交成功");
    }
}
