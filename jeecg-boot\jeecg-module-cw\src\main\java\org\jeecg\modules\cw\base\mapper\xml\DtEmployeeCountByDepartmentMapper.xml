<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.cw.base.mapper.DtEmployeeCountByDepartmentMapper">

    <!-- 统计指定时间范围内的人员总和 -->
    <select id="totalEmployeeCount" resultType="java.lang.Integer">
        SELECT IFNULL(SUM(employee_count), 0)
        FROM dt_employee_count_by_department
        WHERE q_date BETWEEN #{startDate} AND #{endDate}
    </select>

    <!-- 获取指定月份任意一天的人员总和（排除特定单位） -->
    <select id="getFilteredEmployeeCountForMonth" resultType="java.lang.Integer">
        SELECT IFNULL(SUM(employee_count), 0)
        FROM dt_employee_count_by_department
        WHERE q_date = (
            SELECT MIN(q_date)
            FROM dt_employee_count_by_department
            WHERE q_date BETWEEN #{startDate} AND #{endDate}
        )
        AND dep_display_name NOT IN ('新技术', '铸造', '实业', '化工', '铜兴监测')
    </select>

    <!-- 获取指定年份所有有效数据的平均人员数（排除特定单位） -->
    <select id="getFilteredEmployeeCountForYear" resultType="java.lang.Integer">
        SELECT IFNULL(ROUND(AVG(daily_total)), 0)
        FROM (
            SELECT q_date, SUM(employee_count) as daily_total
            FROM dt_employee_count_by_department
            WHERE q_date BETWEEN #{startDate} AND #{endDate}
            AND dep_display_name NOT IN ('新技术', '铸造', '实业', '化工', '铜兴监测')
            GROUP BY q_date
            HAVING SUM(employee_count) > 0
        ) daily_stats
    </select>

</mapper> 