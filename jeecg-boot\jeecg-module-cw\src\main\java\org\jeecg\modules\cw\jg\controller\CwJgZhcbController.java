package org.jeecg.modules.cw.jg.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.cw.jg.param.CwJgZhcbSumbitParam;
import org.jeecg.modules.cw.jg.result.CwJgZhcbListResult;
import org.jeecg.modules.cw.jg.service.ICwJgZhcbService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Description: 机关-综合成本表
 * @Author: tang
 * @Date: 2024-12-06
 * @Version: V1.0
 */
@Api(tags = "机关-综合财报")
@RestController
@RequestMapping("/jg/zhcb")
@Slf4j
public class CwJgZhcbController {

    @Resource
    private ICwJgZhcbService jgZhcbService;


    @ApiOperation(value = "机关-综合成本-列表查询", notes = "机关-综合成本-列表查询")
    @GetMapping(value = "/query")
    public Result<CwJgZhcbListResult> query(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        CwJgZhcbListResult result = jgZhcbService.query(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "机关-综合成本-自动填充", notes = "机关-综合成本-自动填充")
    @GetMapping(value = "/autoFill")
    public Result<CwJgZhcbListResult> autoFill(@DateTimeFormat(pattern="yyyy-MM-dd") Date queryDate) {
        CwJgZhcbListResult result = jgZhcbService.autoFill(queryDate);
        return Result.ok(result);
    }

    @ApiOperation(value = "机关-综合成本-提交", notes = "机关-综合成本-提交")
    @PostMapping(value = "/submit")
    public Result<String> submit(@RequestBody CwJgZhcbSumbitParam submitParam) {
        jgZhcbService.submit(submitParam);
        return Result.OK("提交成功");
    }
}
