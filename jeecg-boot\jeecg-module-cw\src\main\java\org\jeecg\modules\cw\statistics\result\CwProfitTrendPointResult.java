package org.jeecg.modules.cw.statistics.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 利润趋势点数据
 */
@Data
@ApiModel("利润趋势点")
public class CwProfitTrendPointResult {

    /** 时间标识：yyyy-MM-dd 或 yyyy-MM */
    @ApiModelProperty("时间标识")
    private String period;

    /** 利润值（万元） */
    @ApiModelProperty("利润，单位：万元")
    private BigDecimal profit;
} 