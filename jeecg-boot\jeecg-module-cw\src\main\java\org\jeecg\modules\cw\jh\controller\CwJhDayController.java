package org.jeecg.modules.cw.jh.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.cw.jh.entity.CwJhDay;
import org.jeecg.modules.cw.jh.service.ICwJhDayService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 检化中心-日填报
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
@Api(tags="检化中心-日填报")
@RestController
@RequestMapping("/jh/cwJhDay")
@Slf4j
public class CwJhDayController extends JeecgController<CwJhDay, ICwJhDayService> {
	@Autowired
	private ICwJhDayService cwJhDayService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwJhDay
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "检化中心-日填报-分页列表查询")
	@ApiOperation(value="检化中心-日填报-分页列表查询", notes="检化中心-日填报-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CwJhDay>> queryPageList(CwJhDay cwJhDay,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwJhDay> queryWrapper = QueryGenerator.initQueryWrapper(cwJhDay, req.getParameterMap());
		Page<CwJhDay> page = new Page<CwJhDay>(pageNo, pageSize);
		IPage<CwJhDay> pageList = cwJhDayService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwJhDay
	 * @return
	 */
	@AutoLog(value = "检化中心-日填报-添加")
	@ApiOperation(value="检化中心-日填报-添加", notes="检化中心-日填报-添加")
	@RequiresPermissions("jh:cw_jh_day:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwJhDay cwJhDay) {
		cwJhDayService.save(cwJhDay);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwJhDay
	 * @return
	 */
	@AutoLog(value = "检化中心-日填报-编辑")
	@ApiOperation(value="检化中心-日填报-编辑", notes="检化中心-日填报-编辑")
	@RequiresPermissions("jh:cw_jh_day:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwJhDay cwJhDay) {
		cwJhDayService.updateById(cwJhDay);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "检化中心-日填报-通过id删除")
	@ApiOperation(value="检化中心-日填报-通过id删除", notes="检化中心-日填报-通过id删除")
	@RequiresPermissions("jh:cw_jh_day:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwJhDayService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "检化中心-日填报-批量删除")
	@ApiOperation(value="检化中心-日填报-批量删除", notes="检化中心-日填报-批量删除")
	@RequiresPermissions("jh:cw_jh_day:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwJhDayService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "检化中心-日填报-通过id查询")
	@ApiOperation(value="检化中心-日填报-通过id查询", notes="检化中心-日填报-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwJhDay> queryById(@RequestParam(name="id",required=true) String id) {
		CwJhDay cwJhDay = cwJhDayService.getById(id);
		if(cwJhDay==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwJhDay);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwJhDay
    */
    @RequiresPermissions("jh:cw_jh_day:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwJhDay cwJhDay) {
        return super.exportXls(request, cwJhDay, CwJhDay.class, "检化中心-日填报");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("jh:cw_jh_day:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwJhDay.class);
    }

}
