package org.jeecg.modules.cw.base.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.cw.base.entity.CwCllCblData;
import org.jeecg.modules.cw.base.service.ICwCllCblDataService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 处理量采拨量数据
 * @Author: jeecg-boot
 * @Date:   2025-03-12
 * @Version: V1.0
 */
@Api(tags="处理量采拨量数据")
@RestController
@RequestMapping("/base/cwCllCblData")
@Slf4j
public class CwCllCblDataController extends JeecgController<CwCllCblData, ICwCllCblDataService> {
	@Autowired
	private ICwCllCblDataService cwCllCblDataService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwCllCblData
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "处理量采拨量数据-分页列表查询")
	@ApiOperation(value="处理量采拨量数据-分页列表查询", notes="处理量采拨量数据-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CwCllCblData>> queryPageList(CwCllCblData cwCllCblData,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwCllCblData> queryWrapper = QueryGenerator.initQueryWrapper(cwCllCblData, req.getParameterMap());
		Page<CwCllCblData> page = new Page<CwCllCblData>(pageNo, pageSize);
		IPage<CwCllCblData> pageList = cwCllCblDataService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwCllCblData
	 * @return
	 */
	@AutoLog(value = "处理量采拨量数据-添加")
	@ApiOperation(value="处理量采拨量数据-添加", notes="处理量采拨量数据-添加")
	@RequiresPermissions("base:cw_cll_cbl_data:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwCllCblData cwCllCblData) {
		cwCllCblDataService.save(cwCllCblData);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwCllCblData
	 * @return
	 */
	@AutoLog(value = "处理量采拨量数据-编辑")
	@ApiOperation(value="处理量采拨量数据-编辑", notes="处理量采拨量数据-编辑")
	@RequiresPermissions("base:cw_cll_cbl_data:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwCllCblData cwCllCblData) {
		cwCllCblDataService.updateById(cwCllCblData);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "处理量采拨量数据-通过id删除")
	@ApiOperation(value="处理量采拨量数据-通过id删除", notes="处理量采拨量数据-通过id删除")
	@RequiresPermissions("base:cw_cll_cbl_data:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwCllCblDataService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "处理量采拨量数据-批量删除")
	@ApiOperation(value="处理量采拨量数据-批量删除", notes="处理量采拨量数据-批量删除")
	@RequiresPermissions("base:cw_cll_cbl_data:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwCllCblDataService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "处理量采拨量数据-通过id查询")
	@ApiOperation(value="处理量采拨量数据-通过id查询", notes="处理量采拨量数据-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwCllCblData> queryById(@RequestParam(name="id",required=true) String id) {
		CwCllCblData cwCllCblData = cwCllCblDataService.getById(id);
		if(cwCllCblData==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwCllCblData);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwCllCblData
    */
    @RequiresPermissions("base:cw_cll_cbl_data:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwCllCblData cwCllCblData) {
        return super.exportXls(request, cwCllCblData, CwCllCblData.class, "处理量采拨量数据");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("base:cw_cll_cbl_data:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwCllCblData.class);
    }

}
