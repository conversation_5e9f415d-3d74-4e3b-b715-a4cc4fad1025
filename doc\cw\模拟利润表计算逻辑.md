# 模拟利润表计算逻辑文档

> 适用范围：`cw_mnlr` **日表** (`CwMnlrDay`/前端 `views/cw/mnlr/day`)
>       `cw_mnlr` **月表** (`CwMnlrMonth`/前端 `views/cw/mnlr/month`)

---

## 一、核心字段
| 字段 | 含义 | 备注 |
| ---- | ---- | ---- |
| `jg` | 金属实际价格（含税） | 单位：元/吨（公斤） |
| `xs` | 公司实际结算系数 | 0‒1的小数，前端展示 *100% |
| `xl` | 实际销量 | 单位：吨（公斤） |
| `cb` | 金属总成本 | 单位：万元 |
| `qtfy` | 其他费用等 | 单位：万元，整表唯一值 |
| `frdw` | 分公司及多经单位利润 | 单位：万元，整表唯一值 |
| `tzlr` | 实际销量与产量差调整利润 | 仅 **月表** 使用，单位：万元 |
| `jh` | 年度公司进度计划 | 单位：万元（**整年**值） |

> 备注：表格按照金属类型分 5 行明细：
> - `t` 含铜
> - `j` 含金
> - `y` 含银
> - `l` 含硫
> - `ljk` 硫精矿（折标 35%）

---

## 二、单项销售收入公式
对任意金属条目 `data`（含 *jg/xs/xl* 三字段）：

\[
\text{SingleSales} = \frac{jg \times xs \times xl}{1.13 \times 10000}
\]

- 1.13：将含税价格转为不含税价（13% 增值税率）。
- 10000：元→万元的单位换算。

---

## 三、日报计算逻辑
```mermaid
graph TD;
A[jg/xs/xl 输入] --> B[单项销售收入];
B --> C[销售收入合计 slSum];
D[cb 输入] --> E[成本合计 cbSum];
C --> F[全矿利润 mnlr];
E --> F;
G[qtfy] --> F;
H[frdw] --> F;
F --> I[与进度计划比 jhb];
```

1. **销售收入合计**：
   \[
   slSum = \sum_{metal}(SingleSales_{metal})
   \]

2. **成本合计**：
   \[
   cbSum = \sum_{metal}(cb_{metal})
   \]

3. **全矿模拟利润（日）**：
   \[
   mnlr = slSum - cbSum - qtfy + frdw
   \]

4. **公司进度计划（日）**：
   \[
   gsjh = \frac{jh}{12 \times daysInMonth}
   \]
   - `daysInMonth` 为当月天数，由 `dayjs(queryDate).daysInMonth()` 计算。

5. **与进度计划比**：
   \[
   jhb = mnlr - gsjh
   \]

> 以上公式对应后端 `MnlrStatisticsUtil.calculateStatistics` 与前端 `views/cw/mnlr/day/index.vue` 实现。

---

## 四、月报计算逻辑
与日报一致，差异如下：

1. **调整利润**：月表额外字段 `tzlr`（实际销量与产量差调整利润）。

2. **全矿模拟利润（月）**：
   \[
   mnlr_{month} = slSum - cbSum - qtfy + frdw + tzlr
   \]

3. **公司进度计划（月）**：
   \[
   gsjh_{month} = \frac{jh}{12}
   \]

4. **与进度计划比（月）**：
   \[
   jhb_{month} = mnlr_{month} - gsjh_{month}
   \]

---

## 五、参考代码片段
```java
// MnlrStatisticsUtil.java
// 1. 单项销售收入
a = jg.multiply(xs).multiply(xl)
        .divide(new BigDecimal("1.13"), RoundingMode.HALF_UP)
        .divide(new BigDecimal("10000"), RoundingMode.HALF_UP);

// 2. 全矿利润（日）
BigDecimal mnlr = slSum.subtract(cbSum).subtract(qtfy).add(frdw);

// 3. 公司进度计划（月）
BigDecimal gsjh = jh.divide(new BigDecimal("12"), 2, RoundingMode.HALF_UP);
```

---

## 六、数据流向概览
1. **前端录入 / 自动拉取** → `cwMnlrDay` / `cwMnlrMonth` 表。
2. **提交接口**：
   - 日：`mnlr/cwMnlrDay/submit`
   - 月：`mnlr/cwMnlrMonth/submit`
3. **后台统计**：`MnlrStatisticsUtil` 计算并持久化到 `CwMnlrStatisticsDay` 表。 