package org.jeecg.modules.cw.mnlr.param;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrDayRow;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrMonthRow;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CwMnlrDaySumbitParam {
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;
    private List<CwMnlrDayRow> rows;
    private BigDecimal qtfy;
    private BigDecimal frdw;
    private BigDecimal jh;
}
