package org.jeecg.modules.cw.krb.service;


import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.cw.krb.entity.CwKrb;
import org.jeecg.modules.cw.krb.param.CwKrbSumbitParam;
import org.jeecg.modules.cw.krb.result.CwKrbQueryResult;

import java.util.Date;

/**
 * @Description: 矿成本日报表
 * @Author: jeecg-boot
 * @Date:   2024-12-31
 * @Version: V1.0
 */
public interface ICwKrbService extends IService<CwKrb> {

    CwKrbQueryResult queryByDate(Date queryDate);

    void submit(CwKrbSumbitParam submitParam);
}
