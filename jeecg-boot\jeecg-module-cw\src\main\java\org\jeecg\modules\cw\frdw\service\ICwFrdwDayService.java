package org.jeecg.modules.cw.frdw.service;

import org.jeecg.modules.cw.frdw.entity.CwFrdwDay;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * @Description: 法人单位-日填报
 * @Author: jeecg-boot
 * @Date:   2024-12-31
 * @Version: V1.0
 */
public interface ICwFrdwDayService extends IService<CwFrdwDay> {

    List<CwFrdwDay> autoFill(Date queryDate);
}
