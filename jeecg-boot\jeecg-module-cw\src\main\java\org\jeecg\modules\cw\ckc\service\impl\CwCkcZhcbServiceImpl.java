package org.jeecg.modules.cw.ckc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.cw.base.constants.CwCllDataName;
import org.jeecg.modules.cw.base.entity.CwCllCblData;
import org.jeecg.modules.cw.base.entity.CwDwBase;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.entity.CwCllycData;
import org.jeecg.modules.cw.base.service.ICwCllCblDataService;
import org.jeecg.modules.cw.base.service.ICwCllDataService;
import org.jeecg.modules.cw.base.service.ICwDwBaseService;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.base.service.ICwCllycDataService;
import org.jeecg.modules.cw.ckc.entity.CwCkcDay;
import org.jeecg.modules.cw.ckc.entity.CwCkcMonth;
import org.jeecg.modules.cw.ckc.entity.CwCkcRow;
import org.jeecg.modules.cw.ckc.mapper.CwCkcDayMapper;
import org.jeecg.modules.cw.ckc.service.ICwCkcDayService;
import org.jeecg.modules.cw.ckc.service.ICwCkcMonthService;
import org.jeecg.modules.cw.ckc.param.CwCkcZhcbSumbitParam;
import org.jeecg.modules.cw.ckc.result.CwCkcZhcbListResult;
import org.jeecg.modules.cw.ckc.service.ICwCkcZhcbService;
import org.jeecg.modules.cw.jg.entity.CwJgDay;
import org.jeecg.modules.cw.krb.entity.CwKrbRow;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrStatisticsDayService;
import org.jeecg.modules.cw.common.ForecastChangeDetector;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import javax.annotation.PreDestroy;

/**
 * @Description: 采矿场-日填报
 * @Author: jeecg-boot
 * @Date: 2024-12-06
 * @Version: V1.0
 */
@Service
@Slf4j
public class CwCkcZhcbServiceImpl implements ICwCkcZhcbService {

    private static final String DICT_TYPE = "ckc";
    private static final String CACHE_KEY = "cw:ckc:zhcb:";
    private static final String QUERY_CACHE_KEY = CACHE_KEY + "query";
    private static final String SUM_DRS_CACHE_KEY = CACHE_KEY + "sumDrs";
    private static final String SUM_BY_MONTH_CACHE_KEY = CACHE_KEY + "sumByMonth";
    private static final String NAME_CKC = "ckc";

    @Resource
    private ICwNameDictService nameDictService;
    @Resource
    private ICwCkcDayService ckcDayService;
    @Resource
    private ICwCkcMonthService ckcMonthService;
    @Resource
    private ICwCllDataService cwCllDataService;
    @Resource
    private ICwCllycDataService cwCllycDataService;
    @Lazy
    @Resource
    private ICwMnlrStatisticsDayService mnlrStatisticsDayService;

    /** 异步执行器：单线程 顺序重算 */
    private final ExecutorService drsRecalcExecutor = Executors.newSingleThreadExecutor(new ThreadFactory() {
        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, "ckc-drs-recalc");
            t.setDaemon(true);
            return t;
        }
    });

    @PreDestroy
    public void shutdown() {
        drsRecalcExecutor.shutdown();
    }

    @Override
    public CwCkcZhcbListResult query(Date queryDate) {
        // 1. 初始化结果对象
        CwCkcZhcbListResult result = new CwCkcZhcbListResult();
        result.setQueryDate(queryDate);
        
        // 2. 获取基础数据
        // 2.1 日期相关
        Date beginOfMonth = DateUtil.beginOfMonth(queryDate);
        Date endOfDay = DateUtil.endOfDay(queryDate);
        Date dayBeforeQuery = DateUtil.offsetDay(queryDate, -1);
        int currentDay = DateUtil.dayOfMonth(queryDate);
        boolean isFirstHalf = currentDay <= 15;
        
        // 2.2 项目字典
        List<CwNameDict> dict = nameDictService.queryList(DICT_TYPE);

        // 新增: 获取当月处理量预测(cllyc)，后续用于自动补全缺失日数据
        BigDecimal monthCllyc = null;
        {
            Date monthBegin = DateUtil.beginOfMonth(queryDate);
            CwCllycData cllycData = cwCllycDataService.lambdaQuery()
                    .eq(CwCllycData::getRecordTime, monthBegin)
                    .eq(CwCllycData::getName, NAME_CKC)
                    .one();
            if (ObjectUtil.isNotEmpty(cllycData)) {
                monthCllyc = cllycData.getCllyc();
            }
        }
        
        // 2.3 日表和月表数据
        List<CwCkcDay> allDaysInMonth = ckcDayService.lambdaQuery()
                .between(CwCkcDay::getRecordTime, beginOfMonth, endOfDay)
                .list();
        List<CwCkcMonth> months = ckcMonthService.lambdaQuery()
                .ge(CwCkcMonth::getRecordTime, beginOfMonth)
                .le(CwCkcMonth::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();

        // === 新增逻辑: 若存在处理量预测, 自动补全缺失的日数据 ===
        if (ObjectUtil.isNotEmpty(monthCllyc) && monthCllyc.compareTo(BigDecimal.ZERO) > 0) {
            List<CwCkcDay> filled = this.autoFillMissingDays(beginOfMonth, queryDate, dict, monthCllyc, months);
            if (ObjectUtil.isNotEmpty(filled)) {
                allDaysInMonth.addAll(filled);
            }
        }
        
        // 将日数据按名称分组
        Map<String, List<CwCkcDay>> daysByName = allDaysInMonth.stream().collect(Collectors.groupingBy(CwCkcDay::getName));
        
        // 3. 构建结果行数据
        List<CwCkcRow> resRows = new ArrayList<>();
        
        for (CwNameDict d : dict) {
            CwCkcRow row = new CwCkcRow();
            BeanUtil.copyProperties(d, row);
            
            // 3.1 填充月数据
            months.stream()
                  .filter(m -> d.getName().equals(m.getName()))
                  .findFirst()
                  .ifPresent(m -> fillMonthData(row, m, isFirstHalf));
            
            List<CwCkcDay> itemDays = daysByName.get(d.getName());
            if (ObjectUtil.isNotEmpty(itemDays)) {
                // 3.2 填充当日日数据和当日数
                itemDays.stream()
                        .filter(day -> DateUtil.isSameDay(queryDate, day.getRecordTime()))
                        .findFirst()
                        .ifPresent(day -> {
                            fillDayData(row, day);
                            if (day.getDrs() != null) {
                                row.setDrs(day.getDrs().toString());
                            }
                        });
                
                // 3.4 计算月累计 (直接累加drs)
                BigDecimal ylj = itemDays.stream()
                        .filter(day -> !day.getRecordTime().after(dayBeforeQuery))
                        .map(CwCkcDay::getDrs)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                row.setYlj(ylj.toString());
            }
            
            resRows.add(row);
        }
        
        result.setRows(resRows);
        
        // 4. 设置额外数据 (这部分数据用于展示，仍需获取)
        // 4.1 处理量 - 使用CwCllData服务获取采拨总量
        Map<String, String> cbzlDataMap = cwCllDataService.getRangeCllData(CwCllDataName.CBZL, queryDate, queryDate);
        if(ObjectUtil.isNotEmpty(cbzlDataMap)){
            String todayKey = DateUtil.format(queryDate, "yyyy-MM-dd");
            String cbzlStr = cbzlDataMap.get(todayKey);
            if(ObjectUtil.isNotEmpty(cbzlStr)){
                result.setCbzl(cbzlStr);
            }
        }
        
        // 4.3 处理量预测
        CwCllycData cllycData = cwCllycDataService.lambdaQuery()
                .eq(CwCllycData::getRecordTime, beginOfMonth)
                .eq(CwCllycData::getName, NAME_CKC)
                .one();
        if (ObjectUtil.isNotEmpty(cllycData) && ObjectUtil.isNotEmpty(cllycData.getCllyc())) {
            result.setYc(cllycData.getCllyc().toPlainString());
        }
        
        return result;
    }

    @Override
    public void submit(CwCkcZhcbSumbitParam param) {
        Date submitDate = param.getSubmitDate();
        // 标记本次是否修改了处理量预测（cllyc）
        boolean cllycChanged = false;
        // 标记预测数据变更情况
        boolean anyForecastChanged = false;
        boolean needsFullMonthRecalc = false;
        boolean needsOnlyLastDayRecalc = false;

        // 月初日期，用于查询/保存处理量预测
        Date monthBegin = DateUtil.beginOfMonth(submitDate);
        
        // 更新采拨总量数据到cw_cll_data表
        if (param.getBase().getCbzl() != null) {
            cwCllDataService.setCllData(CwCllDataName.CBZL, param.getBase().getCbzl().toPlainString(), submitDate);
        }
        
        // ========== 处理量预测(cllyc) 的增、改、删 ==========
        BigDecimal newCllyc = param.getBase().getCllyc();
        CwCllycData cllycData = cwCllycDataService.lambdaQuery()
                .eq(CwCllycData::getRecordTime, monthBegin)
                .eq(CwCllycData::getName, NAME_CKC)
                .one();

        if (newCllyc != null) {
            // 新值不为空 -> 新增或修改
            if (cllycData == null) {
                cllycData = new CwCllycData();
                cllycData.setRecordTime(monthBegin);
                cllycData.setName(NAME_CKC);
                cllycChanged = true;
            } else if (cllycData.getCllyc() == null || cllycData.getCllyc().compareTo(newCllyc) != 0) {
                // 值发生变化
                cllycChanged = true;
            }

            cllycData.setCllyc(newCllyc);
            // 显式区分新增或更新，避免使用 saveOrUpdate 带来的额外开销
            if (cllycData.getId() == null) {
                cwCllycDataService.save(cllycData);
            } else {
                cwCllycDataService.updateById(cllycData);
            }
        } else {
            // 新值为空 -> 需要删除已有记录
            if (cllycData != null) {
                cwCllycDataService.removeById(cllycData.getId());
                cllycChanged = true;
            }
        }
        
        // 更新日数据
        List<CwCkcRow> rows = param.getRows();
        ckcDayService.remove(new LambdaQueryWrapper<>(CwCkcDay.class).eq(CwCkcDay::getRecordTime, submitDate));
        
        // 获取已有的月数据
        List<CwCkcMonth> existingMonths = ckcMonthService.lambdaQuery()
                .ge(CwCkcMonth::getRecordTime, DateUtil.beginOfMonth(submitDate))
                .le(CwCkcMonth::getRecordTime, DateUtil.endOfMonth(submitDate))
                .list();
        Map<String, CwCkcMonth> existingMonthMap = new HashMap<>();
        for (CwCkcMonth month : existingMonths) {
            existingMonthMap.put(month.getName(), month);
        }
        
        ArrayList<CwCkcDay> days = new ArrayList<>();
        for (CwCkcRow row : rows) {
            CwCkcDay day = new CwCkcDay();
            BeanUtil.copyProperties(row, day);
            day.setRecordTime(submitDate);
            // drs字段将在重算时填充
            day.setDrs(BigDecimal.ZERO);
            days.add(day);
        }
        ckcDayService.saveBatch(days);
        
        ArrayList<CwCkcMonth> months = new ArrayList<>();
        Boolean isFirstHalf = param.getIsFirstHalf();
        
        for (CwCkcRow row : rows) {
            CwCkcMonth month;
            BigDecimal oldYys = null, oldFirstHalf = null, oldSecondHalf = null, oldMonthEnd = null;

            if (existingMonthMap.containsKey(row.getName())) {
                // 使用已存在的月数据
                month = existingMonthMap.get(row.getName());

                // 记录原有预测数据
                oldYys = month.getYys();
                oldFirstHalf = month.getFirstHalfForecast();
                oldSecondHalf = month.getSecondHalfForecast();
                oldMonthEnd = month.getMonthEndForecast();

                // 只更新当前半月的预测值，保留另一半月的预测值
                if (ObjectUtil.isNotEmpty(row.getYyc())) {
                    BigDecimal forecastValue = new BigDecimal(row.getYyc());
                    if (isFirstHalf != null) {
                        if (isFirstHalf) {
                            month.setFirstHalfForecast(forecastValue);
                        } else {
                            month.setSecondHalfForecast(forecastValue);
                        }
                    }
                }

                // 更新其他字段
                if (ObjectUtil.isNotEmpty(row.getPjdj())) {
                    month.setPjdj(new BigDecimal(row.getPjdj()));
                }
                if (ObjectUtil.isNotEmpty(row.getYys())) {
                    month.setYys(new BigDecimal(row.getYys()));
                }
            } else {
                // 创建新的月数据
                month = new CwCkcMonth();
                BeanUtil.copyProperties(row, month);
                month.setRecordTime(submitDate);

                // 根据isFirstHalf设置上半月或下半月预测值
                if (ObjectUtil.isNotEmpty(row.getYyc())) {
                    BigDecimal forecastValue = new BigDecimal(row.getYyc());
                    if (isFirstHalf != null) {
                        if (isFirstHalf) {
                            month.setFirstHalfForecast(forecastValue);
                        } else {
                            month.setSecondHalfForecast(forecastValue);
                        }
                    }
                }
            }

            // 检测预测数据变更
            ForecastChangeDetector.ForecastChangeResult changeResult =
                ForecastChangeDetector.detectChanges(
                    oldYys, month.getYys(),
                    oldFirstHalf, month.getFirstHalfForecast(),
                    oldSecondHalf, month.getSecondHalfForecast(),
                    oldMonthEnd, month.getMonthEndForecast()
                );

            if (changeResult.hasAnyChange()) {
                anyForecastChanged = true;
                if (changeResult.needsFullMonthRecalculation()) {
                    needsFullMonthRecalc = true;
                } else if (changeResult.needsOnlyLastDayRecalculation()) {
                    needsOnlyLastDayRecalc = true;
                }
            }

            months.add(month);
        }
        
        // 批量保存或更新月数据
        ckcMonthService.saveOrUpdateBatch(months);

        // 根据变更情况决定重算范围
        if (cllycChanged || needsFullMonthRecalc) {
            // cllyc变更或预测数据变更需要整个月重算时，重算整个月的 drs
            Date endOfMonth = DateUtil.endOfMonth(submitDate);
            drsRecalcExecutor.submit(() -> recalculateDrs(monthBegin, endOfMonth));
        } else if (needsOnlyLastDayRecalc) {
            // 仅月末预测变更时，只重算月末最后一天
            Date lastDayOfMonth = DateUtil.endOfMonth(submitDate);
            this.recalculateDrsByDate(lastDayOfMonth);
        } else if (anyForecastChanged) {
            // 其他预测数据变更，重算当日
            this.recalculateDrsByDate(submitDate);
        } else {
            // 无预测数据变更，仅重算当日 drs
            this.recalculateDrsByDate(submitDate);
        }
    }

    @Override
    public BigDecimal sumDrs(Date queryDate) {
        // 1. 日期相关
        Date beginOfMonth = DateUtil.beginOfMonth(queryDate);
        Date endOfMonth = DateUtil.endOfMonth(queryDate);

        // 2. 数据准备
        List<CwCkcDay> allDays = ckcDayService.lambdaQuery()
                .between(CwCkcDay::getRecordTime, beginOfMonth, endOfMonth)
                .list();

        // 3. 直接累加drs
        return allDays.stream()
                .map(CwCkcDay::getDrs)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public List<CwKrbRow> sumByMonth(Date queryDate) {
        // 1. 初始化结果
        Map<String, CwKrbRow> result = CwKrbRow.takeDefaultKrbRows("采矿场");
        
        // 2. 获取基础数据
        // 2.1 日期相关
        Date beginOfMonth = DateUtil.beginOfMonth(queryDate);
        Date endOfDay = DateUtil.endOfDay(queryDate);
        
        // 2.2 数据准备
        List<CwCkcDay> allDays = ckcDayService.lambdaQuery()
                .between(CwCkcDay::getRecordTime, beginOfMonth, endOfDay)
                .list();
        List<CwCkcMonth> months = ckcMonthService.lambdaQuery()
                .ge(CwCkcMonth::getRecordTime, beginOfMonth)
                .le(CwCkcMonth::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();
        
        // 3. 定义计算分类行
        CwKrbRow clRow = result.get(CwKrbRow.CL);
        CwKrbRow bjRow = result.get(CwKrbRow.BJ);
        CwKrbRow rlRow = result.get(CwKrbRow.RL);
        CwKrbRow sRow = result.get(CwKrbRow.S);
        CwKrbRow dRow = result.get(CwKrbRow.D);
        CwKrbRow zzfyRow = result.get(CwKrbRow.ZZFY);
        
        // 4. 处理每天的数据
        for (CwCkcDay day : allDays) {
            // 直接获取drs值
            BigDecimal calculatedValue = day.getDrs();

            // 如果drs不为空，则进行累加
            if (ObjectUtil.isNotEmpty(calculatedValue)) {
                // 只有当天的数据才计入当日数
                if (DateUtil.isSameDay(queryDate, day.getRecordTime())) {
                    addToDailyTotal(day.getType(), calculatedValue, clRow, bjRow, rlRow, sRow, dRow, zzfyRow);
                }
                
                // 所有当月数据都计入月累计
                addToMonthlyTotal(day.getType(), calculatedValue, clRow, bjRow, rlRow, sRow, dRow, zzfyRow);
            }
        }
        
        // 5. 处理月预算数据
        for (CwCkcMonth month : months) {
            BigDecimal yys = month.getYys();
            if (ObjectUtil.isNotEmpty(month) && ObjectUtil.isNotEmpty(yys)) {
                addToBudget(month.getType(), yys, clRow, bjRow, rlRow, sRow, dRow, zzfyRow);
            }
        }
        
        return new ArrayList<>(result.values());
    }

    @Override
    public BigDecimal sumBudgetMonth(Date monthDate) {
        BigDecimal total = BigDecimal.ZERO;
        List<CwKrbRow> rows = this.sumByMonth(monthDate);
        if (rows != null) {
            for (CwKrbRow row : rows) {
                if (row != null && row.getYys() != null) {
                    total = total.add(row.getYys());
                }
            }
        }
        return total;
    }

    @Override
    public void recalculateDrsByDate(Date date) {
        log.info("开始重新计算采矿场{}的drs", DateUtil.formatDate(date));
        recalculateDrs(date, date);
        log.info("结束重新计算采矿场{}的drs", DateUtil.formatDate(date));
    }

    @Override
    public void recalculateAllDrs() {
        log.info("开始重新计算采矿场所有的drs");
        // 获取所有有记录的日期
        List<Date> dates = ckcDayService.list().stream()
                .map(CwCkcDay::getRecordTime)
                .map(DateUtil::beginOfDay)
                .distinct()
                .collect(Collectors.toList());

        // 逐日重新计算
        for (Date date : dates) {
            try {
                recalculateDrsByDate(date);
            } catch (Exception e) {
                log.error("重新计算采矿场drs失败，日期: {}", DateUtil.formatDate(date), e);
            }
        }
        log.info("结束重新计算采矿场所有的drs");
    }

    @Override
    public void recalculateTodayDrs() {
        recalculateDrsByDate(new Date());
    }

    /**
     * 更新月末预测数据（仅影响月末最后一天的计算）
     * @param submitDate 提交日期
     * @param monthEndForecasts 月末预测数据 Map<项目名称, 月末预测值>
     */
    public void updateMonthEndForecast(Date submitDate, Map<String, BigDecimal> monthEndForecasts) {
        Date monthBegin = DateUtil.beginOfMonth(submitDate);

        // 获取现有月数据
        List<CwCkcMonth> existingMonths = ckcMonthService.lambdaQuery()
                .eq(CwCkcMonth::getRecordTime, monthBegin)
                .list();

        Map<String, CwCkcMonth> monthMap = existingMonths.stream()
                .collect(Collectors.toMap(CwCkcMonth::getName, m -> m));

        List<CwCkcMonth> monthsToUpdate = new ArrayList<>();
        boolean hasChanges = false;

        for (Map.Entry<String, BigDecimal> entry : monthEndForecasts.entrySet()) {
            String name = entry.getKey();
            BigDecimal newMonthEndForecast = entry.getValue();

            CwCkcMonth month = monthMap.get(name);
            if (month != null) {
                BigDecimal oldMonthEndForecast = month.getMonthEndForecast();
                if (!Objects.equals(oldMonthEndForecast, newMonthEndForecast)) {
                    month.setMonthEndForecast(newMonthEndForecast);
                    monthsToUpdate.add(month);
                    hasChanges = true;
                }
            }
        }

        if (hasChanges) {
            // 批量更新月数据
            ckcMonthService.updateBatchById(monthsToUpdate);

            // 仅重算月末最后一天（月末预测隔离逻辑）
            Date lastDayOfMonth = DateUtil.endOfMonth(submitDate);
            this.recalculateDrsByDate(lastDayOfMonth);
        }
    }

    /**
     * 重新计算指定日期范围内的所有采矿场每日drs
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    private void recalculateDrs(Date startDate, Date endDate) {
        // 1. 查询日期范围内的所有日数据
        List<CwCkcDay> allDaysInRange = ckcDayService.lambdaQuery()
                .between(CwCkcDay::getRecordTime, DateUtil.beginOfDay(startDate), DateUtil.endOfDay(endDate))
                .list();

        if (allDaysInRange.isEmpty()) {
            return;
        }

        // 2. 按月份对数据进行分组
        Map<String, List<CwCkcDay>> daysByMonth = allDaysInRange.stream()
                .collect(Collectors.groupingBy(d -> DateUtil.format(d.getRecordTime(), "yyyy-MM")));

        List<CwCkcDay> daysToUpdate = new ArrayList<>();

        // 3. 按月份处理数据
        for (Map.Entry<String, List<CwCkcDay>> entry : daysByMonth.entrySet()) {
            String monthKey = entry.getKey();
            List<CwCkcDay> daysInMonth = entry.getValue();
            Date monthDate = DateUtil.parse(monthKey, "yyyy-MM");
            Date beginOfMonth = DateUtil.beginOfMonth(monthDate);
            Date endOfMonth = DateUtil.endOfMonth(monthDate);

            // 3.1 获取月表数据
            List<CwCkcMonth> months = ckcMonthService.lambdaQuery()
                    .ge(CwCkcMonth::getRecordTime, beginOfMonth)
                    .le(CwCkcMonth::getRecordTime, endOfMonth)
                    .list();

            // 3.2 获取处理量预测数据
            Map<String, BigDecimal> cllycDataMap = getMonthCllycData(monthDate);
            BigDecimal cllyc = cllycDataMap.get(monthKey);

            // 3.3 获取采剥总量数据
            Map<String, BigDecimal> cbzlDataMap = getRangeCbzlData(beginOfMonth, endOfMonth);

            // 4. 遍历当月的每一天数据，计算drs
            for (CwCkcDay day : daysInMonth) {
                String dateKey = DateUtil.format(day.getRecordTime(), "yyyy-MM-dd");
                BigDecimal cbzl = cbzlDataMap.get(dateKey);

                BigDecimal drs = calculateDrs(day, cllyc, cbzl, months);

                // CwCkcDay 实体中已增加 setDrs 方法
                day.setDrs(drs);
                daysToUpdate.add(day);
            }
        }

        // 5. 批量更新
        if (!daysToUpdate.isEmpty()) {
            ckcDayService.updateBatchById(daysToUpdate);
        }

        // 同步重算模拟利润（日）
        mnlrStatisticsDayService.recalcRange(startDate, endDate);
    }

    @Override
    public CwCkcZhcbListResult autoFill(Date queryDate) {
        this.recalculateDrsByDate(queryDate);
        return query(queryDate);
    }

    /**
     * 自动补全 1 号至查询日期之间缺失的日数据（仅当可用 cllyc 时）
     * @return 新插入的日记录列表
     */
    private List<CwCkcDay> autoFillMissingDays(Date beginOfMonth, Date queryDate,
                                              List<CwNameDict> dict,
                                              BigDecimal monthCllyc,
                                              List<CwCkcMonth> months) {
        Date endOfDay = DateUtil.endOfDay(queryDate);
        // 查询已存在的日数据
        List<CwCkcDay> existing = ckcDayService.lambdaQuery()
                .between(CwCkcDay::getRecordTime, beginOfMonth, endOfDay)
                .list();
        Set<String> existKeys = existing.stream()
                .map(d -> d.getName() + "#" + DateUtil.format(d.getRecordTime(), "yyyy-MM-dd"))
                .collect(Collectors.toSet());

        // 采剥总量
        Map<String, String> cbzlStrMap = cwCllDataService.getRangeCllData(CwCllDataName.CBZL, beginOfMonth, endOfDay);
        Map<String, BigDecimal> cbzlMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(cbzlStrMap)) {
            cbzlStrMap.forEach((k, v) -> {
                if (ObjectUtil.isNotEmpty(v)) {
                    cbzlMap.put(k, new BigDecimal(v));
                }
            });
        }

        List<CwCkcDay> needInsert = new ArrayList<>();
        Date iter = beginOfMonth;
        while (!iter.after(queryDate)) {
            String dateKey = DateUtil.format(iter, "yyyy-MM-dd");
            BigDecimal cbzlVal = cbzlMap.get(dateKey);
            if (ObjectUtil.isEmpty(cbzlVal)) {
                iter = DateUtil.offsetDay(iter, 1);
                continue;
            }
            for (CwNameDict d : dict) {
                String k = d.getName() + "#" + dateKey;
                if (existKeys.contains(k)) {
                    continue;
                }
                CwCkcDay newDay = new CwCkcDay();
                BeanUtil.copyProperties(d, newDay);
                newDay.resetForCopy(); // 清空 id/创建人等, 避免主键重复
                newDay.setRecordTime(iter);
                BigDecimal drsVal = calculateDrs(newDay, monthCllyc, cbzlVal, months);
                newDay.setDrs(drsVal);
                needInsert.add(newDay);
            }
            iter = DateUtil.offsetDay(iter, 1);
        }
        if (!needInsert.isEmpty()) {
            ckcDayService.saveBatch(needInsert);
        }
        return needInsert;
    }

    private BigDecimal calculateDrs(CwCkcDay day, BigDecimal cllyc, BigDecimal cbzl, List<CwCkcMonth> months) {
            String name = day.getName();
            Date recordTime = day.getRecordTime();
            BigDecimal yyc = null;
            BigDecimal pjdj = null;

            // 检查是否为月末最后一天
            boolean isLastDayOfMonth = DateUtil.isSameDay(recordTime, DateUtil.endOfMonth(recordTime));

            for (CwCkcMonth month : months) {
                if (name.equals(month.getName())) {
                    pjdj = month.getPjdj();

                    // 最高优先级：月末预测（仅影响月末最后一天）
                    if (isLastDayOfMonth && month.getMonthEndForecast() != null) {
                        yyc = month.getMonthEndForecast();
                    }
                    // 第二优先级：下半月预测数据（影响整个月）
                    else if (month.getSecondHalfForecast() != null) {
                        yyc = month.getSecondHalfForecast();
                    }
                    // 第三优先级：根据日期使用月预算或下半月预测
                    else {
                        int recordDay = DateUtil.dayOfMonth(recordTime);
                        boolean recordIsFirstHalf = recordDay <= 15;

                        if (recordIsFirstHalf) {
                            // 上半月使用月预算
                            if (month.getYys() != null) {
                                yyc = month.getYys();
                            }
                        } else {
                            // 下半月使用下半月预测（但此时应该为null，因为上面已经检查过）
                            if (month.getSecondHalfForecast() != null) {
                                yyc = month.getSecondHalfForecast();
                            }
                        }
                    }
                    break;
                }
            }
    
            // 只有当月预测/预算、处理量预测和采剥总量都有值时，才计算当日数
            if (ObjectUtil.isNotEmpty(cllyc) && ObjectUtil.isNotEmpty(yyc) &&
                    cllyc.compareTo(BigDecimal.ZERO) > 0 && ObjectUtil.isNotEmpty(cbzl)) {
                // 单位成本 = 月预测或月预算 / 处理量预测
                BigDecimal dwcb = yyc.divide(cllyc, 10, BigDecimal.ROUND_HALF_UP);
                // 当日数 = 单位成本 * 采剥总量
                return dwcb.multiply(cbzl);
            }
    
            BigDecimal pjzh = day.getPjzh();
            if (ObjectUtil.isNotEmpty(pjzh) && ObjectUtil.isNotEmpty(pjdj)) {
                return pjzh.multiply(pjdj);
            }
    
            return BigDecimal.ZERO;
        }

    /**
     * 获取指定月份内所有日期的处理量预测数据
     * @param monthDate 月份日期
     * @return 处理量预测数据
     */
    private Map<String, BigDecimal> getMonthCllycData(Date monthDate) {
        Map<String, BigDecimal> result = new HashMap<>();
        // 获取月初日期
        Date beginOfMonth = DateUtil.beginOfMonth(monthDate);
        String monthKey = DateUtil.format(beginOfMonth, "yyyy-MM");
        
        // 查询处理量预测
        CwCllycData cllycData = cwCllycDataService.lambdaQuery()
                .eq(CwCllycData::getRecordTime, beginOfMonth)
                .eq(CwCllycData::getName, NAME_CKC)
                .one();
        
        if (ObjectUtil.isNotEmpty(cllycData) && ObjectUtil.isNotEmpty(cllycData.getCllyc())) {
            result.put(monthKey, cllycData.getCllyc());
        }
        
        return result;
    }
    
    /**
     * 获取指定日期范围内所有日期的采剥总量数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 采剥总量数据映射，key为日期字符串(yyyy-MM-dd)，value为采剥总量
     */
    private Map<String, BigDecimal> getRangeCbzlData(Date startDate, Date endDate) {
        // 使用CwCllData服务获取日期范围内的采剥总量数据
        Map<String, String> dataMap = cwCllDataService.getRangeCllData(CwCllDataName.CBZL, startDate, endDate);
        
        // 转换为BigDecimal
        Map<String, BigDecimal> result = new HashMap<>();
        if (ObjectUtil.isNotEmpty(dataMap)) {
            dataMap.forEach((dateStr, dataStr) -> {
                if (ObjectUtil.isNotEmpty(dataStr)) {
                    result.put(dateStr, new BigDecimal(dataStr));
                }
            });
        }
        
        return result;
    }

    /**
     * 填充月数据
     * @param row 要填充的行对象
     * @param month 月数据对象
     * @param isFirstHalf 是否为上半月
     */
    private void fillMonthData(CwCkcRow row, CwCkcMonth month, boolean isFirstHalf) {
        // 设置平均单价和月预算
        row.setPjdj(month.getPjdj() == null ? null : month.getPjdj().toString());
        row.setYys(month.getYys() == null ? null : month.getYys().toString());
        
        // 根据当前是上半月还是下半月，返回对应的预测值
        if (!isFirstHalf && month.getSecondHalfForecast() != null) {
            // 仅下半月及月末展示月预测
            row.setYyc(month.getSecondHalfForecast().toString());
        }
    }
    
    /**
     * 填充日数据
     * @param row 要填充的行对象
     * @param day 日数据对象
     */
    private void fillDayData(CwCkcRow row, CwCkcDay day) {
        row.setPjzh(day.getPjzh() == null ? null : day.getPjzh().toString());
        row.setRemark(day.getRemark());
    }
    
    /**
     * 计算月累计
     * @param name 项目名称
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param months 月数据列表
     * @param cllycDataMap 处理量预测数据映射
     * @param cbzlDataMap 采剥总量数据映射
     * @return 月累计值
     */
    private BigDecimal calculateMonthlyAccumulation(
            String name, 
            Date startDate, 
            Date endDate, 
            List<CwCkcMonth> months,
            Map<String, BigDecimal> cllycDataMap,
            Map<String, BigDecimal> cbzlDataMap) {
        
        BigDecimal sum = BigDecimal.ZERO;
        
        // 如果开始日期晚于结束日期，直接返回0
        if (startDate.compareTo(endDate) > 0) {
            return sum;
        }
        
        // 获取指定日期范围内的所有日期（包括开始日期和结束日期）
        List<Date> dateRange = new ArrayList<>();
        Date currentDate = startDate;
        while (!currentDate.after(endDate)) {
            dateRange.add(currentDate);
            currentDate = DateUtil.offsetDay(currentDate, 1);
        }
        
        // 为每个日期计算当日数并累计
        for (Date date : dateRange) {
            // 获取处理量预测
            String dateMonthKey = DateUtil.format(DateUtil.beginOfMonth(date), "yyyy-MM");
            BigDecimal dateCllyc = cllycDataMap.get(dateMonthKey);
            
            // 获取采剥总量
            String dateKey = DateUtil.format(date, "yyyy-MM-dd");
            BigDecimal dateCbzl = cbzlDataMap.get(dateKey);
            
            // 计算当日数
//            BigDecimal dateDrs = calculateDrs(dateCllyc, dateCbzl, months);
            
            // 累加当日数
//            if (ObjectUtil.isNotEmpty(dateDrs)) {
//                sum = sum.add(dateDrs);
//            }
        }
        
        return sum;
    }

    /**
     * 添加到日合计
     */
    private void addToDailyTotal(String type, BigDecimal value, 
                                CwKrbRow clRow, CwKrbRow bjRow, CwKrbRow rlRow, 
                                CwKrbRow sRow, CwKrbRow dRow, CwKrbRow zzfyRow) {
        switch (type) {
            case "cl":
                clRow.setDrs(clRow.getDrs().add(value));
                break;
            case "bj":
                bjRow.setDrs(bjRow.getDrs().add(value));
                break;
            case "rl":
                rlRow.setDrs(rlRow.getDrs().add(value));
                break;
            case "d":
                dRow.setDrs(dRow.getDrs().add(value));
                break;
            case "s":
                sRow.setDrs(sRow.getDrs().add(value));
                break;
            case "zzfy":
                zzfyRow.setDrs(zzfyRow.getDrs().add(value));
                break;
        }
    }
    
    /**
     * 添加到月累计
     */
    private void addToMonthlyTotal(String type, BigDecimal value, 
                                  CwKrbRow clRow, CwKrbRow bjRow, CwKrbRow rlRow, 
                                  CwKrbRow sRow, CwKrbRow dRow, CwKrbRow zzfyRow) {
        switch (type) {
            case "cl":
                clRow.setRlj(clRow.getRlj().add(value));
                break;
            case "bj":
                bjRow.setRlj(bjRow.getRlj().add(value));
                break;
            case "rl":
                rlRow.setRlj(rlRow.getRlj().add(value));
                break;
            case "d":
                dRow.setRlj(dRow.getRlj().add(value));
                break;
            case "s":
                sRow.setRlj(sRow.getRlj().add(value));
                break;
            case "zzfy":
                zzfyRow.setRlj(zzfyRow.getRlj().add(value));
                break;
        }
    }
    
    /**
     * 添加到预算
     */
    private void addToBudget(String type, BigDecimal value, 
                            CwKrbRow clRow, CwKrbRow bjRow, CwKrbRow rlRow, 
                            CwKrbRow sRow, CwKrbRow dRow, CwKrbRow zzfyRow) {
        switch (type) {
            case "cl":
                clRow.setYys(clRow.getYys().add(value));
                break;
            case "bj":
                bjRow.setYys(bjRow.getYys().add(value));
                break;
            case "rl":
                rlRow.setYys(rlRow.getYys().add(value));
                break;
            case "d":
                dRow.setYys(dRow.getYys().add(value));
                break;
            case "s":
                sRow.setYys(sRow.getYys().add(value));
                break;
            case "zzfy":
                zzfyRow.setYys(zzfyRow.getYys().add(value));
                break;
        }
    }
}
